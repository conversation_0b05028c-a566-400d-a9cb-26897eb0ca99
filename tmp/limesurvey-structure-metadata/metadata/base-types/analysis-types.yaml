analysisTypes:
- id: ANA
  code: anova_manova_analysis
  name: ANOVA/MANOVA
  description: Analýza rozptylu
  detailedDescription: 'ANOVA (Analysis of Variance) a MANOVA (Multivariate Analysis
    of Variance) jsou statistické metody používané k analýze rozdílů mezi skupinami.
    ANOVA se používá pro jednu zá<PERSON>lou proměnnou, zatímco MANOVA umožňuje analyzovat
    více závislých proměnných současně.


    Tyto metody jsou zvláště užitečné při experimentálním výzkumu, kdy potřebujeme
    porovnat průměry více skupin a určit, zda mezi nimi existují statisticky významné
    rozdíly. ANOVA/MANOVA využívá rozklad celkové variability na meziskupinovou a
    vnitroskupinovou složku, což umožňuje posoudit vliv jednoho nebo více faktorů
    na sledované proměnné.'
  type: section
  supportedQuestionTypes: []
  supportedVisualizations:
  - id: BOX
    code: box_plot
  - id: INT
    code: interaction_plot
  - id: RSP
    code: residual_plot
  - id: QQP
    code: qq_plot
  parameters: []
  status: planned
- id: CLA
  code: cluster_analysis
  name: Klastrová analýza
  description: Seskupování podobných odpovědí
  detailedDescription: 'Klastrová analýza je technika strojového učení, která se používá
    k rozdělení dat do skupin (klastrů) na základě jejich podobnosti. Objekty v jednom
    klastru jsou si navzájem podobnější než objekty z různých klastrů.


    Existuje několik metod klastrování, včetně hierarchického klastrování, k-means,
    DBSCAN a dalších. Výběr metody závisí na povaze dat a cílech analýzy. Klastrová
    analýza se často používá v segmentaci zákazníků, analýze genů nebo kategorizaci
    dokumentů.'
  type: section
  supportedQuestionTypes: []
  supportedVisualizations:
  - id: DEN
    code: dendrogram
  - id: SCP
    code: scatter_plot
  - id: HMP
    code: heat_map
  - id: 3DC
    code: three_d_clustering
  parameters: []
  status: planned
- id: CMA
  code: comparative_analysis
  name: Komparativní analýza
  description: Porovnání mezi skupinami
  detailedDescription: 'Komparativní analýza je metoda systematického porovnávání
    dvou nebo více objektů, jevů nebo datových souborů. Cílem je identifikovat podobnosti
    a rozdíly mezi porovnávanými entitami a porozumět faktorům, které tyto rozdíly
    způsobují.


    Analýza může být kvalitativní i kvantitativní a často kombinuje různé statistické
    metody. Používá se v mnoha oblastech, od businessu přes sociální vědy až po přírodní
    vědy, kde pomáhá identifikovat best practices nebo pochopit rozdíly mezi různými
    skupinami či systémy.'
  type: section
  supportedQuestionTypes:
  - id: '1'
    code: array_dual_scale
  - id: ':'
    code: array_flexible_dropdown
  - id: F
    code: array_flexible_labels
  - id: H
    code: array_flexible_columns
  - id: R
    code: ranking
  supportedVisualizations:
  - id: GBA
    code: grouped_bar_chart
  - id: RAD
    code: radar_chart
  - id: PAC
    code: parallel_coordinates
  - id: BFC
    code: butterfly_chart
  parameters: []
  status: active
- id: COA
  code: context_analysis
  name: Kontextová analýza
  description: Analýza kontextu výskytu
  detailedDescription: 'Kontextová analýza je metoda zkoumání dat v jejich širším
    kontextu, která bere v úvahu okolnosti a podmínky, ve kterých data vznikla nebo
    existují. Tato metoda je zvláště důležitá při analýze textových dat a kvalitativním
    výzkumu.


    Analýza zahrnuje zkoumání vztahů mezi daty a jejich okolím, historickým pozadím,
    kulturními faktory a dalšími kontextuálními proměnnými. Je klíčová pro správnou
    interpretaci dat a pochopení jejich skutečného významu v daném kontextu.'
  type: section
  supportedQuestionTypes:
  - id: ;
    code: array_flexible_text
  - id: O
    code: list_with_comment
  - id: P
    code: multiple_choice_comments
  - id: Q
    code: multiple_short_text
  - id: S
    code: short_free_text
  - id: T
    code: long_free_text
  - id: U
    code: huge_free_text
  supportedVisualizations:
  - id: KWC
    code: kwic_display
  - id: CTR
    code: context_tree
  - id: CNT
    code: collocation_network
  - id: DPG
    code: dependency_graph
  parameters: []
  status: planned
- id: CRA
  code: correlation_analysis
  name: Korelační analýza
  description: Analýza vztahů mezi proměnnými
  detailedDescription: 'Korelační analýza je statistická metoda používaná k měření
    síly a směru vztahu mezi dvěma nebo více proměnnými. Základním výstupem je korelační
    koeficient, který může nabývat hodnot od -1 do +1, kde -1 značí perfektní negativní
    korelaci a +1 perfektní pozitivní korelaci.


    Analýza zahrnuje výpočet různých typů korelačních koeficientů (Pearsonův, Spearmanův,
    Kendallův) v závislosti na povaze dat. Výsledky jsou často vizualizovány pomocí
    bodových grafů nebo korelačních matic, které pomáhají identifikovat vzorce ve
    vztazích mezi proměnnými.'
  type: section
  supportedQuestionTypes:
  - id: '1'
    code: array_dual_scale
  - id: ':'
    code: array_flexible_dropdown
  - id: A
    code: array_five_point
  - id: B
    code: array_ten_point
  - id: F
    code: array_flexible_labels
  - id: H
    code: array_flexible_columns
  - id: K
    code: multiple_numerical
  - id: N
    code: numerical_input
  supportedVisualizations:
  - id: CRM
    code: correlation_matrix
  - id: SPM
    code: scatter_plot_matrix
  - id: BUB
    code: bubble_chart
  - id: HMP
    code: heat_map
  parameters: []
  status: active
- id: CTA
  code: contingency_analysis
  name: Kontingenční analýza
  description: Analýza vztahů mezi kategoriemi
  detailedDescription: 'Kontingenční analýza je statistická metoda používaná k analýze
    vztahů mezi kategoriálními proměnnými. Základním nástrojem je kontingenční tabulka,
    která zobrazuje četnosti kombinací různých kategorií dvou nebo více proměnných.


    Součástí analýzy je často chi-kvadrát test nezávislosti a výpočet různých měr
    asociace (např. Cramerovo V, kontingenční koeficient). Tato metoda je široce využívána
    v sociálních vědách, marketingu a medicíně pro zjišťování závislostí mezi kategoriálními
    proměnnými.'
  type: section
  supportedQuestionTypes:
  - id: '!'
    code: list_dropdown
  - id: '1'
    code: array_dual_scale
  - id: ':'
    code: array_flexible_dropdown
  - id: A
    code: array_five_point
  - id: B
    code: array_ten_point
  - id: C
    code: array_yes_no_uncertain
  - id: E
    code: array_increase_same_decrease
  - id: F
    code: array_flexible_labels
  - id: G
    code: gender_choice
  - id: H
    code: array_flexible_columns
  - id: L
    code: list_radio
  - id: Y
    code: yes_no_choice
  supportedVisualizations:
  - id: CTT
    code: contingency_table
  - id: MOZ
    code: mosaic_plot
  - id: GBA
    code: grouped_bar_chart
  - id: HMP
    code: heat_map
  parameters: []
  status: active
- id: FAA
  code: factor_analysis
  name: Faktorová analýza
  description: Identifikace latentních faktorů
  detailedDescription: 'Faktorová analýza je statistická metoda používaná k identifikaci
    skrytých (latentních) proměnných, které vysvětlují vzory korelací mezi pozorovanými
    proměnnými. Tato technika je často využívána v psychologii, sociologii a marketingu
    k redukci dimenzionality dat a odhalení základních struktur.


    Metoda pracuje na principu extrakce společných faktorů z korelační nebo kovariační
    matice proměnných. Používají se různé metody extrakce faktorů (např. metoda hlavních
    komponent, metoda maximální věrohodnosti) a rotace faktorů pro lepší interpretovatelnost
    výsledků.'
  type: section
  supportedQuestionTypes:
  - id: A
    code: array_five_point
  - id: B
    code: array_ten_point
  supportedVisualizations:
  - id: SCP
    code: scatter_plot
  - id: BIP
    code: biplot
  - id: LDP
    code: loading_plot
  - id: FAM
    code: factor_map
  parameters: []
  status: planned
- id: GEA
  code: geographic_analysis
  name: Geografická analýza
  description: Analýza geografického rozložení
  detailedDescription: 'Geografická analýza se zabývá prostorovými vztahy a vzorci
    v datech s geografickou složkou. Využívá techniky geografických informačních systémů
    (GIS) k vizualizaci a analýze dat v kontextu jejich geografické polohy.


    Tato analýza zahrnuje mapování dat, prostorovou statistiku, analýzu hot-spotů,
    a geografické clustering. Často se používá v urbanistice, ekologii, epidemiologii
    a marketingu pro pochopení prostorových vzorců a vztahů mezi různými geografickými
    jevy.'
  type: section
  supportedQuestionTypes: []
  supportedVisualizations:
  - id: GMP
    code: geo_point_map
  - id: GMH
    code: geo_heat_map
  - id: GMB
    code: geo_bubble_map
  - id: GMC
    code: geo_choropleth_map
  parameters: []
  status: planned
- id: NTA
  code: network_analysis
  name: Síťová analýza
  description: Analýza vztahů mezi prvky
  detailedDescription: 'Síťová analýza je metoda studia vztahů a interakcí mezi entitami
    v síti (grafu). Využívá teorii grafů k analýze struktury a vlastností sítí, kde
    uzly představují entity a hrany reprezentují vztahy mezi nimi.


    Analýza zahrnuje výpočet různých metrik (centralita, hustota, clustering koeficient)
    a identifikaci důležitých vzorců v síti. Používá se v mnoha oblastech, od analýzy
    sociálních sítí přes studium biologických systémů až po analýzu dopravních nebo
    komunikačních sítí.'
  type: section
  supportedQuestionTypes:
  - id: M
    code: multiple_choice
  supportedVisualizations:
  - id: NET
    code: network_graph
  - id: CHD
    code: chord_diagram
  - id: SAN
    code: sankey_diagram
  - id: FDG
    code: force_directed_graph
  parameters: []
  status: planned
- id: TMA
  code: time_analysis
  name: Časová analýza
  description: Analýza vývoje v čase
  detailedDescription: 'Časová analýza je metoda zkoumání dat seřazených chronologicky,
    která se zaměřuje na identifikaci trendů, sezónnosti, cyklů a dalších časově závislých
    vzorců v datech. Tato analýza je klíčová pro předpovídání budoucího vývoje na
    základě historických dat.




    Při časové analýze se často využívají techniky jako dekompozice časových řad,
    klouzavé průměry, exponenciální vyhlazování nebo složitější modely jako ARIMA
    (Autoregressive Integrated Moving Average). Tyto metody pomáhají odhalit skryté
    vzory v datech a umožňují vytvářet přesnější predikce budoucího vývoje.'
  type: section
  supportedQuestionTypes:
  - id: D
    code: date_input
  - id: E
    code: array_increase_same_decrease
  supportedVisualizations:
  - id: TML
    code: timeline
  - id: STG
    code: stream_graph
  - id: BPC
    code: bump_chart
  - id: ANV
    code: animated_visualization
  parameters: []
  status: active
- id: DIA
  code: distribution_analysis
  name: Distribuční analýza
  description: Analýza rozložení hodnot
  detailedDescription: 'Distribuční analýza se zaměřuje na zkoumání rozložení hodnot
    v datovém souboru a jejich pravděpodobnostní charakteristiky. Tato metoda pomáhá
    pochopit, jak jsou data rozložena kolem středních hodnot a jaká je jejich variabilita.


    Analýza zahrnuje výpočet základních statistických charakteristik (průměr, medián,
    rozptyl), testování normality dat a vizualizaci pomocí histogramů, boxplotů nebo
    Q-Q plotů. Tyto informace jsou klíčové pro výběr vhodných statistických metod
    a porozumění základní struktuře dat.'
  type: question
  supportedQuestionTypes:
  - id: '5'
    code: five_point_choice
  - id: K
    code: multiple_numerical
  - id: N
    code: numerical_input
  supportedVisualizations:
  - id: HIS
    code: histogram
  - id: DEN
    code: dendrogram
  - id: BOX
    code: box_plot
  - id: VIP
    code: violin_plot
  parameters: []
  status: active
- id: FRA
  code: frequency_analysis
  name: Frekvenční analýza
  description: Počítání výskytů slov, frází nebo hodnot
  detailedDescription: 'Frekvenční analýza se zabývá studiem četnosti výskytu různých
    hodnot nebo kategorií v datovém souboru. Je základním nástrojem pro pochopení
    struktury kategorických dat a identifikaci dominantních vzorců v datech.


    Součástí analýzy je vytváření frekvenčních tabulek, výpočet relativních četností
    a kumulativních četností. Výsledky jsou často vizualizovány pomocí sloupcových
    grafů, koláčových grafů nebo Paretových diagramů, což umožňuje rychlé pochopení
    distribuce dat.'
  type: question
  supportedQuestionTypes:
  - id: '!'
    code: list_dropdown
  - id: '5'
    code: five_point_choice
  - id: C
    code: array_yes_no_uncertain
  - id: G
    code: gender_choice
  - id: L
    code: list_radio
  - id: M
    code: multiple_choice
  - id: O
    code: list_with_comment
  - id: P
    code: multiple_choice_comments
  - id: Y
    code: yes_no_choice
  supportedVisualizations:
  - id: PAR
    code: pareto_chart
  - id: BAR
    code: bar_chart
  - id: TFT
    code: frequency_table
  - id: BUB
    code: bubble_chart
  parameters: []
  status: active
- id: NGA
  code: ngram_analysis
  name: N-gram analýza
  description: Analýza častých slovních spojení
  detailedDescription: 'N-gram analýza je technika používaná v zpracování přirozeného
    jazyka (NLP) ke studiu sekvencí n po sobě jdoucích prvků (nejčastěji slov nebo
    znaků) v textu. N-gramy mohou být unigramy (jednotlivá slova), bigramy (dvojice
    slov), trigramy (trojice slov) atd.


    Tato analýza je klíčová pro mnoho aplikací NLP, včetně predikce textu, strojového
    překladu a analýzy stylu. Pomáhá identifikovat běžné fráze, kolokace a vzorce
    v používání jazyka, což je užitečné pro porozumění struktury a významu textu.'
  type: question
  supportedQuestionTypes: []
  supportedVisualizations:
  - id: TNG
    code: ngram_table
  - id: NSG
    code: ngram_network_graph
  - id: HBA
    code: horizontal_bar_chart
  - id: TRM
    code: treemap
  parameters: []
  status: active
- id: QCA
  code: qualitative_coding
  name: Kvalitativní kódování
  description: Systematická kategorizace odpovědí
  detailedDescription: 'Kvalitativní kódování je proces systematické organizace a
    kategorizace kvalitativních dat (např. textů, rozhovorů, pozorování) pomocí kódů
    nebo značek. Tyto kódy reprezentují témata, koncepty nebo významy obsažené v datech.


    Proces zahrnuje několik fází: otevřené kódování (identifikace základních témat),
    axiální kódování (hledání vztahů mezi kódy) a selektivní kódování (integrace kódů
    do teoretického rámce). Tato metoda je základním nástrojem kvalitativního výzkumu
    a pomáhá transformovat nestrukturovaná data do analyzovatelné podoby.'
  type: question
  supportedQuestionTypes: []
  supportedVisualizations:
  - id: CMX
    code: code_matrix
  - id: CRB
    code: code_relations
  - id: CLP
    code: code_line_plot
  - id: ALL
    code: alluvial_diagram
  parameters: []
  status: active
- id: RAA
  code: ranking_analysis
  name: Ranking analýza
  description: Analýza pořadí a preferencí
  detailedDescription: 'Ranking analýza je metoda používaná k seřazení položek podle
    určitých kritérií nebo metrik. Může zahrnovat jednoduché řazení podle jednoho
    kritéria nebo složitější multi-kriteriální hodnocení.


    Tato analýza často využívá různé váhy pro různá kritéria a může zahrnovat normalizaci
    dat pro spravedlivé porovnání. Je široce používána v hodnocení výkonnosti, benchmarkingu
    a rozhodovacích procesech, kde je potřeba seřadit alternativy podle jejich relativní
    důležitosti nebo výkonu.'
  type: question
  supportedQuestionTypes:
  - id: E
    code: array_increase_same_decrease
  - id: R
    code: ranking
  supportedVisualizations:
  - id: WAT
    code: waterfall_chart
  - id: DIV
    code: diverging_bars
  - id: DUM
    code: dumbbell_plot
  - id: HMP
    code: heat_map
  parameters: []
  status: active
- id: SNA
  code: sentiment_analysis
  name: Sentiment analýza
  description: Hodnocení emočního zabarvení odpovědí
  detailedDescription: 'Sentiment analýza je technika zpracování přirozeného jazyka,
    která se používá k určení emocionálního zabarvení nebo postoje v textu. Může identifikovat
    pozitivní, negativní nebo neutrální sentiment, případně i složitější emoční stavy.


    Analýza může být založena na různých přístupech, od jednoduchého slovníkového
    přístupu až po pokročilé metody strojového učení. Často se používá pro analýzu
    sociálních médií, zákaznických recenzí a průzkumů spokojenosti, kde pomáhá porozumět
    náladám a postojům uživatelů nebo zákazníků.'
  type: question
  supportedQuestionTypes: []
  supportedVisualizations:
  - id: BAR
    code: bar_chart
  - id: TSL
    code: timeline_simple
  - id: HMP
    code: heat_map
  - id: PIE
    code: pie_chart
  parameters: []
  status: active
- id: STA
  code: summary_statistics
  name: Summary statistics
  description: Základní statistické ukazatele
  detailedDescription: 'Summary statistics (souhrnná statistika) poskytuje základní
    statistické charakteristiky datového souboru. Zahrnuje míry centrální tendence
    (průměr, medián, modus), míry variability (rozptyl, směrodatná odchylka) a míry
    tvaru distribuce (šikmost, špičatost).


    Tyto statistiky jsou základním nástrojem pro rychlé pochopení hlavních charakteristik
    dat a jsou často výchozím bodem pro hlubší analýzu. Pomáhají identifikovat odlehlé
    hodnoty, asymetrie v datech a poskytují první pohled na rozložení dat.'
  type: question
  supportedQuestionTypes:
  - id: '5'
    code: five_point_choice
  - id: D
    code: date_input
  - id: K
    code: multiple_numerical
  - id: N
    code: numerical_input
  supportedVisualizations:
  - id: BOX
    code: box_plot
  - id: VIP
    code: violin_plot
  - id: HIS
    code: histogram
  - id: DEN
    code: dendrogram
  parameters: []
  status: active
- id: THA
  code: thematic_analysis
  name: Tematická analýza
  description: Identifikace a kategorizace opakujících se témat
  detailedDescription: 'Tematická analýza je metoda kvalitativního výzkumu používaná
    k identifikaci, analýze a interpretaci vzorců (témat) v datech. Je flexibilní
    a může být použita napříč různými teoretickými rámci.


    Proces zahrnuje několik fází: seznámení s daty, generování počátečních kódů, hledání
    témat, revize témat, definování a pojmenování témat, a vytvoření závěrečné zprávy.
    Je zvláště užitečná při analýze rozhovorů, focus groups a jiných kvalitativních
    dat.'
  type: question
  supportedQuestionTypes:
  - id: '!'
    code: list_dropdown
  - id: ;
    code: array_flexible_text
  - id: C
    code: array_yes_no_uncertain
  - id: L
    code: list_radio
  - id: M
    code: multiple_choice
  - id: O
    code: list_with_comment
  - id: P
    code: multiple_choice_comments
  - id: Q
    code: multiple_short_text
  - id: S
    code: short_free_text
  - id: T
    code: long_free_text
  - id: U
    code: huge_free_text
  supportedVisualizations:
  - id: HTR
    code: hierarchical_tree
  - id: MMD
    code: mind_map
  - id: TFT
    code: frequency_table
  - id: SUN
    code: sunburst_diagram
  parameters: []
  status: active
- id: TPA
  code: topic_modeling
  name: Topic modeling
  description: Strojové učení pro identifikaci témat
  detailedDescription: 'Topic modeling je technika strojového učení používaná k objevování
    abstraktních témat v kolekcích dokumentů. Využívá statistické modely k identifikaci
    skupin slov, které se často vyskytují společně a reprezentují určité téma.


    Nejčastěji používanou metodou je Latentní Dirichletova Alokace (LDA), která modeluje
    dokumenty jako směsi témat a témata jako směsi slov. Topic modeling se používá
    pro organizaci velkých textových kolekcí, analýzu trendů v dokumentech a pochopení
    hlavních témat diskutovaných v textech.'
  type: question
  supportedQuestionTypes:
  - id: T
    code: long_free_text
  - id: U
    code: huge_free_text
  supportedVisualizations:
  - id: LDA
    code: lda_visualization
  - id: TBC
    code: topic_bubble_chart
  - id: TTS
    code: topic_timeline
  - id: PYR
    code: pyramid_chart
  parameters: []
  status: planned
- id: WCA
  code: word_cloud_analysis
  name: Word Cloud analýza
  description: Analýza četnosti slov a jejich vizuální reprezentace
  detailedDescription: 'Word Cloud (oblak slov) je vizualizační technika používaná
    k zobrazení frekvence výskytu slov v textu. Velikost každého slova v oblaku je
    proporcionální k jeho četnosti výskytu v analyzovaném textu.


    Tato analýza často zahrnuje předzpracování textu (odstranění stop slov, lemmatizace)
    a může být rozšířena o další dimenze jako barvy reprezentující sentiment nebo
    kategorie slov. Je užitečná pro rychlou vizuální analýzu nejdůležitějších témat
    nebo klíčových slov v textu.'
  type: question
  supportedQuestionTypes:
  - id: ;
    code: array_flexible_text
  - id: Q
    code: multiple_short_text
  - id: S
    code: short_free_text
  supportedVisualizations:
  - id: WCS
    code: word_cloud_standard
  - id: WCT
    code: word_cloud_shaped
  - id: WCH
    code: word_cloud_hierarchical
  - id: WCT
    code: word_cloud_shaped
  parameters: []
  status: active
