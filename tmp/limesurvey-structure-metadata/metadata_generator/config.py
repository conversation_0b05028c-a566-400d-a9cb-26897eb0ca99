from pathlib import Path
from typing import Dict, Any
import yaml

DEFAULT_CONFIG = {
    'input_dir': './input',
    'output_dir': './metadata',
    'templates_dir': './templates',
    'default_version': '1.0.0',
    'default_author': 'system'
}

def load_config(config_path: str = 'config.yaml') -> Dict[str, Any]:
    """Load configuration from YAML file or return defaults"""
    config = DEFAULT_CONFIG.copy()
    
    if Path(config_path).exists():
        with open(config_path) as f:
            file_config = yaml.safe_load(f)
            config.update(file_config)
    
    # Ensure paths are Path objects
    config['input_dir'] = Path(config['input_dir'])
    config['output_dir'] = Path(config['output_dir'])
    config['templates_dir'] = Path(config['templates_dir'])
    
    return config
