
# 2024-11-10 00:17:56.299342
+/add *.md

# 2024-11-10 00:18:14.803575
+/add **.md

# 2024-11-10 00:18:57.203566
+ls

# 2024-11-10 00:19:04.065996
+/run ls

# 2024-11-10 00:19:17.993200
+y

# 2024-11-10 00:21:15.559306
+/exit

# 2024-11-10 00:21:50.814930
+/add masterplan.md

# 2024-11-10 00:22:03.454137
+/add *.md

# 2024-11-10 00:23:29.060396
+/add ./*.md

# 2024-11-10 00:24:08.100092
+/add limesurvey-structure-metadata/*.md

# 2024-11-10 00:25:29.985551
+/add

# 2024-11-10 00:25:51.929555
+/add analyzy-otazky.md

# 2024-11-10 00:26:05.224485
+/add analyzy-sekce.md

# 2024-11-10 00:26:10.553126
+/add otazky.md

# 2024-11-10 00:26:20.488752
+/add otazky2analyzy.md

# 2024-11-10 00:26:33.588205
+/add vizualizace.md

# 2024-11-10 00:26:36.616029
+/add

# 2024-11-10 00:27:13.735906
+načti masterplan.md a řiď se pokyny pro vytvoření programu

# 2024-11-10 00:28:29.551204
+/run mkdir -p metadata_generator

# 2024-11-10 00:28:29.673864
+/run python3 -m venv venv

# 2024-11-10 00:28:32.945638
+/run source venv/bin/activate

# 2024-11-10 00:28:33.070920
+/run pip install -r requirements.txt

# 2024-11-10 00:30:48.410310
+spusť program pro odladění

# 2024-11-10 00:31:04.443106
+/run chmod +x metadata_generator/main.py

# 2024-11-10 00:31:04.563099
+/run ./metadata_generator/main.py

# 2024-11-10 00:31:14.219892
+/run mkdir -p input metadata templates

# 2024-11-10 00:31:14.348566
+/run mv *.md input/

# 2024-11-10 00:31:14.479031
+/run ./metadata_generator/main.py

# 2024-11-10 00:31:39.714126
+vyřeš chyby a pokračuj v ladění

# 2024-11-10 00:31:52.122962
+/run pip install -e .

# 2024-11-10 00:31:57.301101
+/run python -m metadata_generator.main

# 2024-11-10 00:33:44.713887
+zjisti chyby, oprav a znovu ladíme

# 2024-11-10 00:37:27.686491
+/run mkdir -p input

# 2024-11-10 00:37:32.851235
+/run python -m metadata_generator.main

# 2024-11-10 00:37:50.873491
+vyřeš chyby

# 2024-11-10 00:38:13.994946
+/run python -m metadata_generator.main

# 2024-11-10 00:38:25.417380
+opět chyby, vyřeš

# 2024-11-10 00:38:49.257326
+/run python -m metadata_generator.main

# 2024-11-10 00:39:12.992194
+vyřeš chybu

# 2024-11-10 00:39:34.024168
+/run python -m metadata_generator.main

# 2024-11-10 00:40:07.174701
+/run python -m metadata_generator.main

# 2024-11-10 00:40:46.718303
+důkladně analyzuj, jako seniorní programátor nalezneš všechny chyby a opravíš je

# 2024-11-10 00:41:25.045962
+/run python -m metadata_generator.main

# 2024-11-10 00:42:03.725512
+/run python -m flake8 --select=E9,F821,F823,F831,F406,F407,F701,F702,F704,F706 metadata_generator/parsers.py

# 2024-11-10 00:42:56.131914
+/tokens

# 2024-11-10 00:46:46.303094
+vypiš mi, jaké soubory s tabulkami načítáš a jaké mají podle tebe sloupce

# 2024-11-10 00:47:13.582608
+/run python -m metadata_generator.main

# 2024-11-10 00:47:21.420495
+/tokens

# 2024-11-10 00:47:25.860129
+n

# 2024-11-10 00:47:29.572764
+/tokens

# 2024-11-10 00:47:45.341667
+/add masterplan.md

# 2024-11-10 00:47:55.044817
+/add otazky.md

# 2024-11-10 00:48:04.612178
+/add analyzy-sekce.nd

# 2024-11-10 00:48:12.122634
+n

# 2024-11-10 00:48:23.948687
+/add analyzy-sekce.md

# 2024-11-10 00:48:34.996326
+/add vizualizace.md

# 2024-11-10 00:48:48.547939
+/add otazky2analyzy.md

# 2024-11-10 00:48:57.331345
+/tokens

# 2024-11-10 00:49:30.418513
+/add analyzy-otazky.md

# 2024-11-10 00:49:34.522724
+/tokens

# 2024-11-10 00:51:42.792540
+znovu prostuduj masterplan.md, podívej se na hlavičky importních souborů v md tabulkách a překontroluj program. Začni načítáním souborů, ověř, zda je soubor přítomný, zda načtení md tabuky funguje. Případně vypisuj do logu více zpráv pro odladění

# 2024-11-10 00:52:40.375780
+/run python3 -m metadata_generator.main

# 2024-11-10 00:53:12.022914
+/run PYTHONPATH=. LOG_LEVEL=DEBUG python3 -m metadata_generator.main

# 2024-11-10 00:53:20.271912
+/run python3 -m flake8 metadata_generator/parsers.py

# 2024-11-10 00:57:58.392831
+analyzuj log a odstraň chyby v programu. Vysvětli, co se děje

# 2024-11-10 00:59:25.479726
+/run python3 -m flake8 metadata_generator/parsers.py

# 2024-11-10 00:59:25.767012
+/run python3 -m metadata_generator.main

# 2024-11-10 00:59:34.670124
+oprav

# 2024-11-10 00:59:57.398828
+/run python3 -m metadata_generator.main

# 2024-11-10 01:00:11.357578
+/run python3 -m flake8 metadata_generator/parsers.py

# 2024-11-10 01:01:24.013408
+/run python3 -m metadata_generator.main

# 2024-11-10 01:01:44.419689
+/run python3 -m flake8 metadata_generator/parsers.py

# 2024-11-10 01:03:06.434575
+zkontroluj a spusť k ladění

# 2024-11-10 01:03:14.794071
+/run pip install -e .

# 2024-11-10 01:03:24.263638
+/run mkdir -p input metadata templates

# 2024-11-10 01:03:26.226511
+/run PYTHONPATH=. python3 -m metadata_generator.main

# 2024-11-10 01:03:35.953168
+oprav

# 2024-11-10 01:03:54.913872
+/run PYTHONPATH=. python3 -m metadata_generator.main

# 2024-11-10 01:04:26.656595
+popiš mi, kde je problém

# 2024-11-10 01:06:23.710698
+/run PYTHONPATH=. python3 -m metadata_generator.main

# 2024-11-10 01:08:34.379086
+vysvětli mi podrobněji, nerozumím větě: Problém je v souboru input/analyzy-sekce.md a input/analyzy-otazky.md, kde jsou duplicitní tabulky na konci souboru. Tyto duplicitní řádky způsobují problém při parsování. Jaké duplicitní tabulky? Ano, tabulky mají stejnou strukturu, z nich se ale generuje jiný yaml soubor a jsou umístěny v různých adresářích, viz masterplan.md. V čem je přesně problém? Mám tabulky nějak upravit? Já ale chyby ve struktuře tabulek nevidím

# 2024-11-10 01:11:20.319330
+cat /input vizualizace.md

# 2024-11-10 01:11:47.246646
+run cat /input/vizualizace.md

# 2024-11-10 01:11:54.666762
+/run cat input/vizualizace.md

# 2024-11-10 01:12:36.331980
+n

# 2024-11-10 01:16:13.216983
+/add

# 2024-11-10 01:16:36.560015
+/clear

# 2024-11-10 01:16:47.847867
+/reset

# 2024-11-10 01:16:54.008066
+/tokens

# 2024-11-10 01:17:30.094746
+/add input/*.md

# 2024-11-10 01:20:32.988440
+/tokens

# 2024-11-10 01:23:13.592684
+/add input/vizualizace.md input/otazky2analyzy.md input/analyzy-otazky.md metadata_generator/config.py

# 2024-11-10 01:23:27.260831
+/add input

# 2024-11-10 01:23:36.679169
+/add

# 2024-11-10 01:23:42.902326
+/test

# 2024-11-10 01:23:46.199361
+/tokens

# 2024-11-10 01:25:48.606611
+/add metadata_generation.log

# 2024-11-10 01:25:48.721346
+/drop input

# 2024-11-10 01:27:33.778581
+spust a oprav chyby

# 2024-11-10 01:28:06.138493
+/run pip install -e .

# 2024-11-10 01:28:11.248001
+/run python -m metadata_generator.main

# 2024-11-10 01:28:26.921417
+kde je problém?popiš

# 2024-11-10 01:29:26.120602
+/run python -m metadata_generator.main

# 2024-11-10 01:29:46.270868
+kde je nyní problém?

# 2024-11-10 01:29:55.880810
+/run python -m metadata_generator.main

# 2024-11-10 01:30:06.759787
+kde je chyba?

# 2024-11-10 01:30:32.183095
+/run python -m metadata_generator.main

# 2024-11-10 01:31:52.018422
+/add input/otazky.md

# 2024-11-10 01:32:08.892640
+/run python -m metadata_generator.main

# 2024-11-10 01:33:11.585587
+/drop input/otazky.md
