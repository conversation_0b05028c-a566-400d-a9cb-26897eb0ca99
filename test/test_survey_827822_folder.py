#!/usr/bin/env python3
"""
Test pro survey 827822 - fáze 1

Najde složku 827822 v Datawrapper, spo<PERSON><PERSON><PERSON><PERSON> grafy a vyp<PERSON><PERSON><PERSON> j<PERSON> n<PERSON>.
"""

import os
import sys
import logging
from pathlib import Path

# Přidání src do Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_survey_827822_folder():
    """Test fáze 1: Najít složku 827822 a vypsat grafy"""
    
    try:
        logger.info("=" * 60)
        logger.info("TEST SURVEY 827822 - FÁZE 1")
        logger.info("=" * 60)
        
        from datawrapper_export import DatawrapperExportClient
        from config_loader import load_config
        
        # Načten<PERSON> konfigurace
        config = load_config()
        parent_folder_id = config.get('DATAWRAPPER_LIMESURVEY_FOLDER_ID')
        team_id = config.get('DATAWRAPPER_TEAM_ID')
        
        logger.info(f"Parent folder ID (LimeSurvey): {parent_folder_id}")
        logger.info(f"Team ID: {team_id}")
        
        # Survey info
        survey_id = "827822"
        known_folder_id = "329553"  # Z Menu 9 implementace
        
        logger.info(f"Survey ID: {survey_id}")
        logger.info(f"Známé folder ID: {known_folder_id}")
        
        # Inicializace klienta
        client = DatawrapperExportClient()
        
        # Krok 1: Ověření parent složky
        logger.info("\n🔍 KROK 1: Ověření parent složky")
        
        parent_contents = client.get_folder_contents(parent_folder_id)
        
        if parent_contents:
            folders = [item for item in parent_contents if item.get('type') == 'folder']
            charts = [item for item in parent_contents if item.get('type') == 'chart']
            
            logger.info(f"✅ Parent složka obsahuje:")
            logger.info(f"   📁 Složek: {len(folders)}")
            logger.info(f"   📊 Grafů: {len(charts)}")
            
            # Zobrazení složek
            logger.info("\n📁 Složky v parent složce:")
            for folder in folders:
                folder_id = folder.get('id')
                folder_name = folder.get('title', folder.get('name', 'Bez názvu'))
                logger.info(f"   - ID: {folder_id}, Název: '{folder_name}'")
                
                # Kontrola, zda je to naše hledaná složka
                if str(folder_id) == known_folder_id:
                    logger.info(f"     🎯 TOTO JE NAŠE SLOŽKA PRO SURVEY {survey_id}!")
                elif survey_id in folder_name:
                    logger.info(f"     🔍 Možná shoda podle názvu")
        else:
            logger.error(f"❌ Nepodařilo se načíst parent složku {parent_folder_id}")
            return False
        
        # Krok 2: Načtení cílové složky
        logger.info(f"\n📊 KROK 2: Načtení složky {known_folder_id}")
        
        folder_contents = client.get_folder_contents(known_folder_id)
        
        if not folder_contents:
            logger.error(f"❌ Složka {known_folder_id} je prázdná nebo neexistuje")
            return False
        
        # Analýza obsahu
        charts_in_folder = [item for item in folder_contents if item.get('type') == 'chart']
        subfolders = [item for item in folder_contents if item.get('type') == 'folder']
        
        logger.info(f"✅ Složka {known_folder_id} obsahuje:")
        logger.info(f"   📊 Grafů: {len(charts_in_folder)}")
        logger.info(f"   📁 Podsložek: {len(subfolders)}")
        
        # Krok 3: Detailní informace o grafech
        logger.info(f"\n📋 KROK 3: Seznam grafů ve složce")
        
        if charts_in_folder:
            logger.info(f"Nalezeno {len(charts_in_folder)} grafů:")
            
            for i, chart in enumerate(charts_in_folder, 1):
                chart_id = chart.get('id', 'N/A')
                chart_title = chart.get('title', 'Bez názvu')
                chart_type = chart.get('type', 'unknown')
                chart_status = chart.get('publicStatus', 'draft')
                
                logger.info(f"   {i:2d}. {chart_title}")
                logger.info(f"       ID: {chart_id}")
                logger.info(f"       Typ: {chart_type}")
                logger.info(f"       Status: {chart_status}")
                
                # Pokud je publikován, zkusíme získat URL
                if chart_status == 'published':
                    public_url = chart.get('publicUrl', 'N/A')
                    logger.info(f"       URL: {public_url}")
                
                logger.info("")
        else:
            logger.warning("⚠️ Ve složce nejsou žádné grafy")
        
        # Krok 4: Test pomocí DatawrapperExportClient metod
        logger.info(f"\n🧪 KROK 4: Test pomocí export klienta")
        
        # Test find_survey_folder
        found_folder_id = client.find_survey_folder(survey_id)
        
        if found_folder_id:
            logger.info(f"✅ find_survey_folder() nalezla: {found_folder_id}")
            
            if found_folder_id == known_folder_id:
                logger.info("✅ Shoda s očekávaným ID!")
            else:
                logger.warning(f"⚠️ Rozdíl: očekáváno {known_folder_id}, nalezeno {found_folder_id}")
        else:
            logger.warning("⚠️ find_survey_folder() nenalezla složku")
        
        # Test get_charts_in_folder
        charts_via_method = client.get_charts_in_folder(known_folder_id)
        
        logger.info(f"✅ get_charts_in_folder() nalezla: {len(charts_via_method)} grafů")
        
        if charts_via_method:
            logger.info("📋 Grafy přes export metodu:")
            for i, chart in enumerate(charts_via_method[:5], 1):  # Prvních 5
                logger.info(f"   {i}. {chart.title} (ID: {chart.id}, Status: {chart.status})")
            
            if len(charts_via_method) > 5:
                logger.info(f"   ... a dalších {len(charts_via_method) - 5} grafů")
        
        # Shrnutí
        logger.info("\n" + "=" * 60)
        logger.info("SHRNUTÍ FÁZE 1")
        logger.info("=" * 60)
        logger.info(f"Survey ID: {survey_id}")
        logger.info(f"Folder ID: {known_folder_id}")
        logger.info(f"Celkem grafů: {len(charts_in_folder)}")
        logger.info(f"Publikovaných grafů: {sum(1 for c in charts_in_folder if c.get('publicStatus') == 'published')}")
        logger.info(f"Export metoda funguje: {'✅ ANO' if charts_via_method else '❌ NE'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_alternative_folder_discovery():
    """Test alternativních metod hledání složky"""
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("TEST ALTERNATIVNÍCH METOD HLEDÁNÍ")
        logger.info("=" * 60)
        
        from datawrapper_client import DatawrapperClient
        from config_loader import load_config
        
        config = load_config()
        parent_folder_id = config.get('DATAWRAPPER_LIMESURVEY_FOLDER_ID')
        
        client = DatawrapperClient()
        survey_id = "827822"
        
        # Test get_folder_contents na parent složce
        logger.info(f"🔍 Testování get_folder_contents na parent složce {parent_folder_id}")
        
        contents = client.get_folder_contents(parent_folder_id)
        
        if contents:
            logger.info(f"✅ Načteno {len(contents)} položek z parent složky")
            
            # Hledání podle názvu
            for item in contents:
                if item.get('type') == 'folder':
                    folder_name = item.get('title', item.get('name', ''))
                    folder_id = item.get('id')
                    
                    if survey_id in folder_name:
                        logger.info(f"🎯 Nalezena složka podle názvu: '{folder_name}' (ID: {folder_id})")
        else:
            logger.warning("⚠️ get_folder_contents nevrátila žádný obsah")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při alternativním testu: {str(e)}")
        return False


if __name__ == "__main__":
    print("🚀 SPOUŠTÍM TEST SURVEY 827822 - FÁZE 1")
    
    # Test hlavní funkcionality
    success1 = test_survey_827822_folder()
    
    # Test alternativních metod
    success2 = test_alternative_folder_discovery()
    
    if success1 and success2:
        print("\n🎉 FÁZE 1 ÚSPĚŠNĚ DOKONČENA!")
        print("✅ Složka 827822 nalezena a analyzována")
        print("✅ Grafy spočítány a vypsány")
        print("🚀 Připraveno pro fázi 2: Konfigurace metadata a HTML export")
    else:
        print("\n❌ FÁZE 1 SELHALA!")
        print("💡 Zkontrolujte konfiguraci a připojení k Datawrapper")
