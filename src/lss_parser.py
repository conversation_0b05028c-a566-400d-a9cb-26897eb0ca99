"""
LSS Parser

Parser pro LimeSurvey LSS soubory.
Extrahuje otázky a strukturu z LSS XML souborů.
"""

import os
import json
import logging
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class LSSQuestion:
    """Reprezentace otázky z LSS souboru"""
    qid: str
    question_code: str
    question_text: str
    question_type: str
    group_id: str = ""
    subquestions: List[Dict] = None
    answer_options: List[Dict] = None
    attributes: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.subquestions is None:
            self.subquestions = []
        if self.answer_options is None:
            self.answer_options = []
        if self.attributes is None:
            self.attributes = {}


class LSSParser:
    """Parser pro LSS soubory"""
    
    def __init__(self):
        """Inicializace parseru"""
        self.lss_cache: Dict[str, ET.Element] = {}
        logger.info("LSSParser inicializován")
    
    def parse_lss_file(self, lss_file_path: str) -> Optional[Dict[str, Any]]:
        """
        Parsuje LSS soubor a vrátí strukturovaná data
        
        Args:
            lss_file_path: Cesta k LSS souboru
            
        Returns:
            Slovník s daty průzkumu nebo None při chybě
        """
        try:
            logger.info(f"🔍 Parsování LSS souboru: {lss_file_path}")
            
            # Kontrola existence souboru
            if not os.path.exists(lss_file_path):
                logger.error(f"❌ LSS soubor neexistuje: {lss_file_path}")
                return None
            
            # Detekce formátu souboru podle obsahu
            format_type = self._detect_file_format(lss_file_path)

            if format_type == 'json':
                return self._parse_json_lss(lss_file_path)
            elif format_type == 'xml':
                return self._parse_xml_lss(lss_file_path)
            else:
                logger.error(f"❌ Nepodporovaný formát LSS souboru: {lss_file_path}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Chyba při parsování LSS souboru: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def _detect_file_format(self, lss_file_path: str) -> str:
        """Detekuje formát LSS souboru podle obsahu"""
        try:
            with open(lss_file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()

            # JSON začíná {
            if first_line.startswith('{'):
                return 'json'
            # XML začíná < nebo <?xml
            elif first_line.startswith('<'):
                return 'xml'
            # Fallback podle přípony
            elif lss_file_path.endswith('.json'):
                return 'json'
            elif lss_file_path.endswith('.lss'):
                # Většina LSS souborů je XML, ale někdy JSON
                # Zkusíme JSON parsing jako fallback
                return 'json'
            else:
                return 'unknown'

        except Exception as e:
            logger.warning(f"⚠️ Chyba při detekci formátu: {str(e)}")
            return 'unknown'
    
    def _parse_json_lss(self, lss_file_path: str) -> Optional[Dict[str, Any]]:
        """Parsuje LSS soubor ve formátu JSON"""
        try:
            with open(lss_file_path, 'r', encoding='utf-8') as f:
                structure = json.load(f)
            
            logger.info(f"📄 Načten JSON LSS soubor")
            
            # Extrakce otázek z JSON struktury
            questions = []
            
            for question_data in structure.get('questions', []):
                question = self._extract_question_from_json(question_data)
                if question:
                    questions.append(question)
            
            survey_data = {
                'survey_id': structure.get('survey_id', 'unknown'),
                'questions': questions,
                'groups': structure.get('groups', []),
                'metadata': {
                    'total_questions': len(questions),
                    'format': 'json',
                    'source_file': lss_file_path
                }
            }
            
            logger.info(f"✅ Parsování dokončeno: {len(questions)} otázek")
            return survey_data
            
        except Exception as e:
            logger.error(f"❌ Chyba při parsování JSON LSS: {str(e)}")
            return None
    
    def _parse_xml_lss(self, lss_file_path: str) -> Optional[Dict[str, Any]]:
        """Parsuje LSS soubor ve formátu XML"""
        try:
            # Načtení XML
            lss_root = self._load_xml_file(lss_file_path)
            if lss_root is None:
                return None
            
            logger.info(f"📄 Načten XML LSS soubor")
            
            # Extrakce otázek z XML
            questions = []
            
            # Hledání všech otázek v XML
            for question_elem in lss_root.findall(".//question"):
                question = self._extract_question_from_xml(question_elem)
                if question:
                    questions.append(question)
            
            # Extrakce skupin
            groups = []
            for group_elem in lss_root.findall(".//group"):
                group = self._extract_group_from_xml(group_elem)
                if group:
                    groups.append(group)
            
            survey_data = {
                'survey_id': self._extract_survey_id_from_xml(lss_root),
                'questions': questions,
                'groups': groups,
                'metadata': {
                    'total_questions': len(questions),
                    'format': 'xml',
                    'source_file': lss_file_path
                }
            }
            
            logger.info(f"✅ Parsování dokončeno: {len(questions)} otázek, {len(groups)} skupin")
            return survey_data
            
        except Exception as e:
            logger.error(f"❌ Chyba při parsování XML LSS: {str(e)}")
            return None
    
    def _load_xml_file(self, lss_file_path: str) -> Optional[ET.Element]:
        """Načte XML LSS soubor"""
        try:
            if lss_file_path in self.lss_cache:
                return self.lss_cache[lss_file_path]
            
            tree = ET.parse(lss_file_path)
            root = tree.getroot()
            
            self.lss_cache[lss_file_path] = root
            return root
            
        except Exception as e:
            logger.error(f"❌ Chyba při načítání XML souboru {lss_file_path}: {e}")
            return None
    
    def _extract_question_from_json(self, question_data: Dict) -> Optional[LSSQuestion]:
        """Extrahuje otázku z JSON dat"""
        try:
            qid = str(question_data.get('qid', ''))
            if not qid:
                return None
            
            question = LSSQuestion(
                qid=qid,
                question_code=question_data.get('title', qid),
                question_text=question_data.get('question', ''),
                question_type=question_data.get('type', ''),
                group_id=str(question_data.get('gid', '')),
                subquestions=question_data.get('subquestions', []),
                answer_options=question_data.get('answeroptions', []),
                attributes=question_data.get('attributes', {})
            )
            
            return question
            
        except Exception as e:
            logger.warning(f"⚠️ Chyba při extrakci otázky z JSON: {str(e)}")
            return None
    
    def _extract_question_from_xml(self, question_elem: ET.Element) -> Optional[LSSQuestion]:
        """Extrahuje otázku z XML elementu"""
        try:
            qid = question_elem.get('qid', '')
            if not qid:
                return None
            
            # Základní informace
            question_type = question_elem.get('type', '')
            question_code = question_elem.get('title', qid)
            group_id = question_elem.get('gid', '')
            
            # Text otázky
            question_text = ''
            question_text_elem = question_elem.find('question_text')
            if question_text_elem is not None:
                question_text = question_text_elem.text or ''
            
            # Subotázky
            subquestions = []
            for subq_elem in question_elem.findall('.//subquestion'):
                subq_data = {
                    'code': subq_elem.get('title', ''),
                    'text': subq_elem.find('question_text').text if subq_elem.find('question_text') is not None else '',
                    'scale_id': subq_elem.get('scale_id', '0')
                }
                subquestions.append(subq_data)
            
            # Možnosti odpovědí
            answer_options = []
            for answer_elem in question_elem.findall('.//answer'):
                answer_data = {
                    'code': answer_elem.get('code', ''),
                    'text': answer_elem.text or '',
                    'scale_id': answer_elem.get('scale_id', '0')
                }
                answer_options.append(answer_data)
            
            # Atributy
            attributes = {}
            for attr_elem in question_elem.findall('.//attribute'):
                attr_name = attr_elem.get('attribute', '')
                attr_value = attr_elem.text or ''
                if attr_name:
                    attributes[attr_name] = attr_value
            
            question = LSSQuestion(
                qid=qid,
                question_code=question_code,
                question_text=question_text,
                question_type=question_type,
                group_id=group_id,
                subquestions=subquestions,
                answer_options=answer_options,
                attributes=attributes
            )
            
            return question
            
        except Exception as e:
            logger.warning(f"⚠️ Chyba při extrakci otázky z XML: {str(e)}")
            return None
    
    def _extract_group_from_xml(self, group_elem: ET.Element) -> Optional[Dict]:
        """Extrahuje skupinu z XML elementu"""
        try:
            gid = group_elem.get('gid', '')
            if not gid:
                return None
            
            group_name = ''
            group_name_elem = group_elem.find('group_name')
            if group_name_elem is not None:
                group_name = group_name_elem.text or ''
            
            group_description = ''
            group_desc_elem = group_elem.find('description')
            if group_desc_elem is not None:
                group_description = group_desc_elem.text or ''
            
            return {
                'gid': gid,
                'group_name': group_name,
                'description': group_description,
                'group_order': group_elem.get('group_order', '0')
            }
            
        except Exception as e:
            logger.warning(f"⚠️ Chyba při extrakci skupiny z XML: {str(e)}")
            return None
    
    def _extract_survey_id_from_xml(self, lss_root: ET.Element) -> str:
        """Extrahuje ID průzkumu z XML"""
        try:
            # Hledání survey ID v různých místech
            survey_elem = lss_root.find('.//survey')
            if survey_elem is not None:
                sid = survey_elem.get('sid', '')
                if sid:
                    return sid
            
            # Fallback - hledání v atributech root elementu
            return lss_root.get('sid', 'unknown')
            
        except Exception as e:
            logger.warning(f"⚠️ Chyba při extrakci survey ID: {str(e)}")
            return 'unknown'
    
    def get_questions_by_type(self, survey_data: Dict[str, Any], question_type: str) -> List[LSSQuestion]:
        """Vrátí otázky podle typu"""
        if not survey_data or 'questions' not in survey_data:
            return []
        
        return [q for q in survey_data['questions'] if q.question_type == question_type]
    
    def get_question_by_id(self, survey_data: Dict[str, Any], qid: str) -> Optional[LSSQuestion]:
        """Vrátí otázku podle ID"""
        if not survey_data or 'questions' not in survey_data:
            return None
        
        for question in survey_data['questions']:
            if question.qid == qid:
                return question
        
        return None
    
    def get_survey_statistics(self, survey_data: Dict[str, Any]) -> Dict[str, Any]:
        """Vrátí statistiky průzkumu"""
        if not survey_data:
            return {}
        
        questions = survey_data.get('questions', [])
        
        # Počítání typů otázek
        question_types = {}
        for question in questions:
            q_type = question.question_type
            if q_type not in question_types:
                question_types[q_type] = 0
            question_types[q_type] += 1
        
        return {
            'total_questions': len(questions),
            'total_groups': len(survey_data.get('groups', [])),
            'question_types': question_types,
            'survey_id': survey_data.get('survey_id', 'unknown'),
            'format': survey_data.get('metadata', {}).get('format', 'unknown')
        }
