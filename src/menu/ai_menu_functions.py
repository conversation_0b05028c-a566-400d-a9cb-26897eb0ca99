"""
AI Menu Functions
Nové AI menu funkce - BEZPEČNÉ, neovlivňují stávající menu
"""

import logging
import os
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


def check_ai_availability() -> bool:
    """
    Kontrola dostupnosti AI funkcí
    BEZPEČNÉ - neovlivňuje stávající kód
    """
    try:
        # Opravené importy - absolutní cesty
        import sys
        import os

        # Přidání src do path pokud tam není
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from ai.integration import get_ai_integration, is_ai_available
        return is_ai_available()
    except ImportError as e:
        logger.error(f"AI import failed: {e}")
        return False
    except Exception as e:
        logger.error(f"AI availability check failed: {e}")
        return False


def display_ai_status():
    """Zobrazí stav AI funkcí"""
    print("\n=== AI Funkce - Stav ===")
    
    if not check_ai_availability():
        print("❌ AI funkce nejsou dostupné")
        print("\n💡 Pro aktivaci AI funkcí:")
        print("   1. Nainstalujte závislosti: pip install -r requirements_ai.txt")
        print("   2. Nastavte OPENAI_API_KEY v .env souboru")
        print("   3. Restartujte aplikaci")
        return False
    
    try:
        # Opravené importy
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from ai.integration import get_ai_integration
        ai = get_ai_integration()

        if ai:
            stats = ai.get_usage_statistics()
            print("✅ AI funkce jsou dostupné")
            print(f"📊 Celkové požadavky: {stats.get('openai', {}).get('total_requests', 0)}")
            print(f"💰 Celkové náklady: ${stats.get('openai', {}).get('total_cost', 0):.4f}")
            print(f"🎯 Cache hit rate: {stats.get('openai', {}).get('cache_hit_rate', 0)}%")
            return True
        else:
            print("⚠️ AI integrace není inicializována")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při kontrole AI stavu: {e}")
        return False


def menu_ai_wordcloud():
    """
    Menu 10: AI WordCloud generování
    BEZPEČNÉ - nová funkce, neovlivňuje stávající menu
    """
    print("\n" + "="*50)
    print("         Menu 10: AI WordCloud Generování")
    print("="*50)
    
    # Kontrola dostupnosti AI
    if not check_ai_availability():
        display_ai_status()
        input("\nStiskněte Enter pro návrat do hlavního menu...")
        return
    
    try:
        # Opravené importy
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from ai.integration import get_ai_integration
        from core.chart_config_manager import create_chart_config_manager
        from core.enhanced_chart_data import create_enhanced_chart_data_manager
        from generators.wordcloud_chart_generator import create_wordcloud_generator
        
        ai = get_ai_integration()
        if not ai:
            print("❌ AI integrace není dostupná")
            input("\nStiskněte Enter pro návrat...")
            return
        
        # Zobrazit aktuální stav
        display_ai_status()
        
        print("\n📋 WordCloud Generování - Kroky:")
        print("1. Výběr survey a textových otázek")
        print("2. Konfigurace AI zpracování")
        print("3. Nastavení vizualizace")
        print("4. Generování a uložení")
        
        # Krok 1: Výběr survey
        print("\n" + "-"*30)
        print("Krok 1: Výběr Survey")
        print("-"*30)
        
        survey_id = input("Zadejte Survey ID: ").strip()
        if not survey_id:
            print("❌ Survey ID je povinné")
            input("\nStiskněte Enter pro návrat...")
            return
        
        # Krok 2: Simulace výběru textových otázek (placeholder)
        print(f"\n📊 Survey: {survey_id}")
        print("🔍 Hledám textové otázky...")
        
        # TODO: Napojit na skutečné načítání dat
        print("⚠️ Placeholder: Simuluji textové otázky")
        sample_questions = {
            "Q1": "Co se vám líbilo na naší službě?",
            "Q2": "Jaké máte návrhy na zlepšení?",
            "Q3": "Další komentáře"
        }
        
        print("\nNalezené textové otázky:")
        for q_id, q_text in sample_questions.items():
            print(f"  {q_id}: {q_text}")
        
        # Výběr otázek
        print("\nVyberte otázky (oddělte čárkou, např. Q1,Q2):")
        selected_input = input("Otázky: ").strip()
        
        if not selected_input:
            selected_questions = list(sample_questions.keys())
            print(f"✅ Použiji všechny otázky: {', '.join(selected_questions)}")
        else:
            selected_questions = [q.strip() for q in selected_input.split(',')]
            print(f"✅ Vybrané otázky: {', '.join(selected_questions)}")
        
        # Krok 3: Konfigurace AI
        print("\n" + "-"*30)
        print("Krok 2: Konfigurace AI")
        print("-"*30)
        
        use_ai = input("Použít AI enhancement? (y/n) [y]: ").strip().lower()
        use_ai = use_ai != 'n'
        
        if use_ai:
            print("🤖 AI enhancement zapnut")
            print("📝 Dostupné prompt templates:")
            print("  1. general_keywords - Obecná klíčová slova")
            print("  2. sentiment_words - Slova podle sentimentu")
            print("  3. categories - Kategorizace odpovědí")
            
            prompt_choice = input("Vyberte template (1-3) [1]: ").strip()
            prompt_templates = {
                '1': 'general_keywords',
                '2': 'sentiment_words', 
                '3': 'categories'
            }
            selected_prompt = prompt_templates.get(prompt_choice, 'general_keywords')
            print(f"✅ Vybrán prompt: {selected_prompt}")
        else:
            print("📊 Použije se základní zpracování")
            selected_prompt = None
        
        # Krok 4: Konfigurace vizualizace
        print("\n" + "-"*30)
        print("Krok 3: Konfigurace Vizualizace")
        print("-"*30)
        
        print("🎨 Dostupné šablony:")
        print("  1. presentation - Pro prezentace (1200x600)")
        print("  2. web - Pro web (800x400)")
        print("  3. print - Pro tisk (2400x1200)")
        print("  4. custom - Vlastní nastavení")
        
        template_choice = input("Vyberte šablonu (1-4) [2]: ").strip()
        templates = {
            '1': 'presentation',
            '2': 'web',
            '3': 'print',
            '4': 'custom'
        }
        selected_template = templates.get(template_choice, 'web')
        
        # Vlastní nastavení
        if selected_template == 'custom':
            try:
                width = int(input("Šířka [800]: ") or "800")
                height = int(input("Výška [400]: ") or "400")
                max_words = int(input("Max. počet slov [200]: ") or "200")
                
                custom_config = {
                    'width': width,
                    'height': height,
                    'max_words': max_words
                }
                print(f"✅ Vlastní konfigurace: {custom_config}")
            except ValueError:
                print("⚠️ Neplatné hodnoty, použiji výchozí")
                selected_template = 'web'
                custom_config = {}
        else:
            custom_config = {}
            print(f"✅ Vybrána šablona: {selected_template}")
        
        # Krok 5: Generování
        print("\n" + "-"*30)
        print("Krok 4: Generování WordCloud")
        print("-"*30)
        
        print("🚀 Spouštím generování...")
        
        # Simulace dat (placeholder)
        sample_text_data = {
            'Q1': ['kvalita služeb', 'rychlé vyřízení', 'příjemný personál', 'dobré ceny'],
            'Q2': ['více možností', 'lepší komunikace', 'rychlejší odpovědi'],
            'Q3': ['celkově spokojen', 'doporučuji ostatním', 'skvělá zkušenost']
        }
        
        # Kombinace textů z vybraných otázek
        combined_texts = []
        for q_id in selected_questions:
            if q_id in sample_text_data:
                combined_texts.extend(sample_text_data[q_id])
        
        combined_text = ' '.join(combined_texts)
        print(f"📝 Zpracovávám {len(combined_texts)} textových odpovědí...")
        
        # Generování WordCloud
        try:
            if use_ai:
                print("🤖 Používám AI enhancement...")
                result = ai.generate_wordcloud_for_survey(
                    survey_id=survey_id,
                    question_ids=selected_questions,
                    use_ai=True
                )
            else:
                print("📊 Používám základní zpracování...")
                # Fallback na základní generování
                result = _generate_basic_wordcloud_fallback(
                    combined_text, 
                    survey_id, 
                    selected_template,
                    custom_config
                )
            
            if result.get('success'):
                print("✅ WordCloud úspěšně vygenerován!")
                print(f"📁 Uloženo do: charts/{survey_id}/wordclouds/")
                
                if 'frequencies' in result:
                    top_words = list(result['frequencies'].items())[:5]
                    print(f"🔝 Top slova: {', '.join([f'{word}({count})' for word, count in top_words])}")
                
                if 'ai_analysis' in result:
                    analysis = result['ai_analysis']
                    if 'summary' in analysis:
                        print(f"🧠 AI analýza: {analysis['summary']}")
                
            else:
                print(f"❌ Chyba při generování: {result.get('error', 'Neznámá chyba')}")
                
        except Exception as e:
            print(f"❌ Chyba při generování WordCloud: {e}")
            logger.error(f"WordCloud generation error: {e}")
        
        # Zobrazit statistiky
        print("\n" + "-"*30)
        print("Statistiky")
        print("-"*30)
        
        try:
            stats = ai.get_usage_statistics()
            openai_stats = stats.get('openai', {})
            print(f"💰 Náklady této operace: ~$0.01-0.05")
            print(f"📊 Celkové náklady: ${openai_stats.get('total_cost', 0):.4f}")
            print(f"🎯 Cache hit rate: {openai_stats.get('cache_hit_rate', 0)}%")
        except:
            print("📊 Statistiky nejsou dostupné")
        
    except ImportError as e:
        print(f"❌ Chybí AI moduly: {e}")
        print("💡 Spusťte: pip install -r requirements_ai.txt")
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {e}")
        logger.error(f"Menu AI WordCloud error: {e}")
    
    input("\nStiskněte Enter pro návrat do hlavního menu...")


def _generate_basic_wordcloud_fallback(
    text: str, 
    survey_id: str, 
    template: str,
    custom_config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Fallback generování WordCloud bez AI
    BEZPEČNÉ - základní funkcionalita
    """
    try:
        from wordcloud import WordCloud
        import matplotlib.pyplot as plt
        from pathlib import Path
        
        # Konfigurace podle šablony
        configs = {
            'presentation': {'width': 1200, 'height': 600, 'max_words': 150},
            'web': {'width': 800, 'height': 400, 'max_words': 200},
            'print': {'width': 2400, 'height': 1200, 'max_words': 300}
        }
        
        config = configs.get(template, configs['web'])
        config.update(custom_config)
        
        # Vytvoření WordCloud
        wordcloud = WordCloud(
            width=config['width'],
            height=config['height'],
            max_words=config['max_words'],
            background_color='white',
            random_state=42
        ).generate(text)
        
        # Uložení
        output_dir = Path(f"charts/{survey_id}/wordclouds")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_path = output_dir / "wordcloud_basic.png"
        wordcloud.to_file(str(output_path))
        
        return {
            'success': True,
            'output_path': str(output_path),
            'frequencies': dict(wordcloud.words_),
            'ai_enhanced': False
        }
        
    except ImportError:
        return {
            'success': False,
            'error': 'WordCloud library not installed. Run: pip install wordcloud'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def menu_chart_type_configuration():
    """
    Menu 21: Konfigurace typů grafů
    BEZPEČNÉ - nová funkce pro konfiguraci chart types
    """
    print("\n" + "="*50)
    print("         Menu 21: Konfigurace Typů Grafů")
    print("="*50)

    try:
        # Opravené importy
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from core.chart_config_manager import create_chart_config_manager

        config_manager = create_chart_config_manager()

        print("\n📋 Dostupné akce:")
        print("1. Zobrazit aktuální konfigurace")
        print("2. Konfigurovat otázku")
        print("3. Reset na výchozí nastavení")
        print("4. Export konfigurace")
        print("5. Import konfigurace")
        print("0. Návrat")

        choice = input("\nVyberte akci (0-5): ").strip()

        if choice == "1":
            _display_current_configurations(config_manager)
        elif choice == "2":
            _configure_question_charts(config_manager)
        elif choice == "3":
            _reset_configurations(config_manager)
        elif choice == "4":
            _export_configuration(config_manager)
        elif choice == "5":
            _import_configuration(config_manager)
        elif choice == "0":
            return
        else:
            print("❌ Neplatná volba")

    except ImportError as e:
        print(f"❌ Chybí moduly: {e}")
    except Exception as e:
        print(f"❌ Chyba: {e}")

    input("\nStiskněte Enter pro pokračování...")


def _display_current_configurations(config_manager):
    """Zobrazí aktuální konfigurace"""
    print("\n" + "-"*30)
    print("Aktuální Konfigurace")
    print("-"*30)

    configurations = config_manager.get_all_configurations()

    if not configurations:
        print("📝 Žádné konfigurace nejsou nastaveny")
        return

    for q_id, config in configurations.items():
        print(f"\n🔹 {q_id}: {config.question_text}")
        print(f"   Typ dat: {config.data_type}")
        print(f"   Skrytá: {'Ano' if config.hidden else 'Ne'}")
        print(f"   Grafy: {', '.join([chart.chart_type for chart in config.charts])}")


def _configure_question_charts(config_manager):
    """Konfigurace grafů pro otázku"""
    print("\n" + "-"*30)
    print("Konfigurace Otázky")
    print("-"*30)

    question_id = input("Zadejte ID otázky: ").strip()
    if not question_id:
        print("❌ ID otázky je povinné")
        return

    question_text = input("Zadejte text otázky: ").strip()
    if not question_text:
        print("❌ Text otázky je povinný")
        return

    print("\nDostupné typy dat:")
    print("1. text - Textové odpovědi")
    print("2. likert - Likertovy škály")
    print("3. choice - Výběr z možností")
    print("4. numeric - Číselné hodnoty")

    data_type_choice = input("Vyberte typ dat (1-4): ").strip()
    data_type_map = {'1': 'text', '2': 'likert', '3': 'choice', '4': 'numeric'}
    data_type = data_type_map.get(data_type_choice, 'text')

    print(f"\n✅ Konfiguruji otázku {question_id} jako {data_type}")

    # Automatická konfigurace
    question_config = config_manager.configure_question_charts(
        question_id, question_text, data_type, auto_configure=True
    )

    print(f"✅ Otázka nakonfigurována s {len(question_config.charts)} grafy:")
    for chart in question_config.charts:
        print(f"   - {chart.chart_type} ({chart.generator})")


def _reset_configurations(config_manager):
    """Reset konfigurací na výchozí"""
    print("\n" + "-"*30)
    print("Reset Konfigurací")
    print("-"*30)

    confirm = input("Opravdu chcete resetovat všechny konfigurace? (y/N): ").strip().lower()
    if confirm == 'y':
        config_manager.reset_to_defaults()
        print("✅ Konfigurace resetovány na výchozí nastavení")
    else:
        print("❌ Reset zrušen")


def _export_configuration(config_manager):
    """Export konfigurace"""
    print("\n" + "-"*30)
    print("Export Konfigurace")
    print("-"*30)

    output_file = input("Zadejte název souboru [chart_config_export.json]: ").strip()
    if not output_file:
        output_file = "chart_config_export.json"

    if config_manager.export_configuration(output_file):
        print(f"✅ Konfigurace exportována do {output_file}")
    else:
        print("❌ Export se nezdařil")


def _import_configuration(config_manager):
    """Import konfigurace"""
    print("\n" + "-"*30)
    print("Import Konfigurace")
    print("-"*30)

    input_file = input("Zadejte název souboru: ").strip()
    if not input_file:
        print("❌ Název souboru je povinný")
        return

    if config_manager.import_configuration(input_file):
        print(f"✅ Konfigurace importována ze {input_file}")
    else:
        print("❌ Import se nezdařil")


def menu_virtual_questions():
    """
    Menu 22: Správa virtuálních otázek
    BEZPEČNÉ - nová funkce pro správu virtual questions
    """
    print("\n" + "="*50)
    print("         Menu 22: Správa Virtuálních Otázek")
    print("="*50)

    try:
        # Opravené importy
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from core.virtual_questions import create_virtual_question_manager

        vq_manager = create_virtual_question_manager()

        print("\n📋 Dostupné akce:")
        print("1. Zobrazit virtuální otázky")
        print("2. Vytvořit virtuální otázku")
        print("3. Toggle viditelnost otázky")
        print("4. Vypočítat data virtuální otázky")
        print("5. Smazat virtuální otázku")
        print("6. Zobrazit merge strategie")
        print("0. Návrat")

        choice = input("\nVyberte akci (0-6): ").strip()

        if choice == "1":
            _display_virtual_questions(vq_manager)
        elif choice == "2":
            _create_virtual_question(vq_manager)
        elif choice == "3":
            _toggle_question_visibility(vq_manager)
        elif choice == "4":
            _compute_virtual_data(vq_manager)
        elif choice == "5":
            _delete_virtual_question(vq_manager)
        elif choice == "6":
            _display_merge_strategies(vq_manager)
        elif choice == "0":
            return
        else:
            print("❌ Neplatná volba")

    except ImportError as e:
        print(f"❌ Chybí moduly: {e}")
    except Exception as e:
        print(f"❌ Chyba: {e}")

    input("\nStiskněte Enter pro pokračování...")


def _display_virtual_questions(vq_manager):
    """Zobrazí virtuální otázky"""
    print("\n" + "-"*30)
    print("Virtuální Otázky")
    print("-"*30)

    virtual_questions = vq_manager.get_all_virtual_questions(include_hidden=True)

    if not virtual_questions:
        print("📝 Žádné virtuální otázky nejsou vytvořeny")
        return

    for vq_id, vq in virtual_questions.items():
        status = "🔒 Skrytá" if vq.get('hidden', False) else "👁️ Viditelná"
        print(f"\n🔹 {vq_id}: {vq.get('question_text', 'N/A')}")
        print(f"   Status: {status}")
        print(f"   Strategie: {vq.get('merge_strategy', 'N/A')}")
        print(f"   Zdrojové otázky: {', '.join(vq.get('source_questions', []))}")

        if 'computed_data' in vq and vq['computed_data']:
            print(f"   ✅ Data vypočítána")
        else:
            print(f"   ⏳ Data nevypočítána")


def _create_virtual_question(vq_manager):
    """Vytvoří virtuální otázku"""
    print("\n" + "-"*30)
    print("Vytvoření Virtuální Otázky")
    print("-"*30)

    question_id = input("Zadejte ID virtuální otázky (např. VIRTUAL_satisfaction): ").strip()
    if not question_id:
        print("❌ ID otázky je povinné")
        return

    question_text = input("Zadejte text otázky: ").strip()
    if not question_text:
        print("❌ Text otázky je povinný")
        return

    print("\nZadejte zdrojové otázky (oddělte čárkou):")
    source_input = input("Zdrojové otázky: ").strip()
    if not source_input:
        print("❌ Zdrojové otázky jsou povinné")
        return

    source_questions = [q.strip() for q in source_input.split(',')]

    print("\nDostupné merge strategie:")
    print("1. concatenate - Spojí textové odpovědi")
    print("2. aggregate_numeric - Agreguje číselné hodnoty")
    print("3. combine_categories - Kombinuje kategorie")
    print("4. cross_tabulate - Křížová tabulka")

    strategy_choice = input("Vyberte strategii (1-4): ").strip()
    strategy_map = {
        '1': 'concatenate',
        '2': 'aggregate_numeric',
        '3': 'combine_categories',
        '4': 'cross_tabulate'
    }
    merge_strategy = strategy_map.get(strategy_choice, 'concatenate')

    hidden = input("Skrýt otázku? (y/N): ").strip().lower() == 'y'

    try:
        virtual_question = vq_manager.create_virtual_question(
            question_id=question_id,
            question_text=question_text,
            source_questions=source_questions,
            merge_strategy=merge_strategy,
            hidden=hidden
        )

        print(f"✅ Virtuální otázka '{question_id}' vytvořena")
        print(f"   Strategie: {merge_strategy}")
        print(f"   Zdrojové otázky: {', '.join(source_questions)}")
        print(f"   Skrytá: {'Ano' if hidden else 'Ne'}")

    except Exception as e:
        print(f"❌ Chyba při vytváření: {e}")


def _toggle_question_visibility(vq_manager):
    """Toggle viditelnost otázky"""
    print("\n" + "-"*30)
    print("Toggle Viditelnost")
    print("-"*30)

    question_id = input("Zadejte ID otázky: ").strip()
    if not question_id:
        print("❌ ID otázky je povinné")
        return

    if vq_manager.toggle_visibility(question_id):
        virtual_question = vq_manager.get_virtual_question(question_id)
        if virtual_question:
            status = "skrytá" if virtual_question.get('hidden', False) else "viditelná"
            print(f"✅ Otázka '{question_id}' je nyní {status}")
        else:
            print(f"❌ Otázka '{question_id}' nebyla nalezena")
    else:
        print(f"❌ Nepodařilo se změnit viditelnost otázky '{question_id}'")


def _compute_virtual_data(vq_manager):
    """Vypočítá data virtuální otázky"""
    print("\n" + "-"*30)
    print("Výpočet Virtuálních Dat")
    print("-"*30)

    question_id = input("Zadejte ID virtuální otázky: ").strip()
    if not question_id:
        print("❌ ID otázky je povinné")
        return

    try:
        print(f"🔄 Počítám data pro '{question_id}'...")
        computed_data = vq_manager.compute_virtual_data(question_id)

        print(f"✅ Data vypočítána pro '{question_id}'")

        # Zobrazit základní statistiky
        if isinstance(computed_data, dict):
            if 'total_responses' in computed_data:
                print(f"   📊 Celkem odpovědí: {computed_data['total_responses']}")
            if 'concatenated_text' in computed_data:
                text_length = len(computed_data['concatenated_text'])
                print(f"   📝 Délka textu: {text_length} znaků")
            if 'aggregated_value' in computed_data:
                print(f"   🔢 Agregovaná hodnota: {computed_data['aggregated_value']}")
            if 'categories' in computed_data:
                print(f"   🏷️ Počet kategorií: {len(computed_data['categories'])}")

    except Exception as e:
        print(f"❌ Chyba při výpočtu: {e}")


def _delete_virtual_question(vq_manager):
    """Smaže virtuální otázku"""
    print("\n" + "-"*30)
    print("Smazání Virtuální Otázky")
    print("-"*30)

    question_id = input("Zadejte ID otázky ke smazání: ").strip()
    if not question_id:
        print("❌ ID otázky je povinné")
        return

    confirm = input(f"Opravdu chcete smazat '{question_id}'? (y/N): ").strip().lower()
    if confirm == 'y':
        if vq_manager.delete_virtual_question(question_id):
            print(f"✅ Virtuální otázka '{question_id}' smazána")
        else:
            print(f"❌ Otázka '{question_id}' nebyla nalezena")
    else:
        print("❌ Smazání zrušeno")


def _display_merge_strategies(vq_manager):
    """Zobrazí dostupné merge strategie"""
    print("\n" + "-"*30)
    print("Dostupné Merge Strategie")
    print("-"*30)

    strategies = vq_manager.get_available_merge_strategies()

    for name, strategy in strategies.items():
        print(f"\n🔹 {name}")
        print(f"   Popis: {strategy.description}")
        print(f"   Podporované typy dat: {', '.join(strategy.data_types)}")
        if strategy.parameters:
            print(f"   Parametry: {', '.join(strategy.parameters.keys())}")


def menu_ai_graph_analysis():
    """
    Menu 23: AI analýza existujících grafů
    BEZPEČNÉ - nová funkce pro AI analýzu grafů
    """
    print("\n" + "="*50)
    print("         Menu 23: AI Analýza Grafů")
    print("="*50)

    # Kontrola dostupnosti AI
    if not check_ai_availability():
        display_ai_status()
        input("\nStiskněte Enter pro návrat do hlavního menu...")
        return

    try:
        # Opravené importy
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from ai.data_analyst import create_ai_data_analyst
        from ai.integration import get_ai_integration

        ai = get_ai_integration()
        if not ai:
            print("❌ AI integrace není dostupná")
            input("\nStiskněte Enter pro návrat...")
            return

        ai_analyst = create_ai_data_analyst(ai)

        print("\n📋 Dostupné akce:")
        print("1. Analyzovat WordCloud data")
        print("2. Analyzovat sloupcový graf")
        print("3. Analyzovat koláčový graf")
        print("4. Analyzovat tabulková data")
        print("5. Batch analýza více grafů")
        print("6. Zobrazit statistiky analýz")
        print("0. Návrat")

        choice = input("\nVyberte akci (0-6): ").strip()

        if choice == "1":
            _analyze_wordcloud_data(ai_analyst)
        elif choice == "2":
            _analyze_column_chart_data(ai_analyst)
        elif choice == "3":
            _analyze_pie_chart_data(ai_analyst)
        elif choice == "4":
            _analyze_table_data(ai_analyst)
        elif choice == "5":
            _batch_analyze_charts(ai_analyst)
        elif choice == "6":
            _display_analysis_statistics(ai_analyst)
        elif choice == "0":
            return
        else:
            print("❌ Neplatná volba")

    except ImportError as e:
        print(f"❌ Chybí AI moduly: {e}")
    except Exception as e:
        print(f"❌ Chyba: {e}")

    input("\nStiskněte Enter pro pokračování...")


def _analyze_wordcloud_data(ai_analyst):
    """Analyzuje WordCloud data"""
    print("\n" + "-"*30)
    print("Analýza WordCloud Dat")
    print("-"*30)

    # Simulace WordCloud dat
    sample_data = {
        'frequencies': {
            'kvalita': 15,
            'služby': 12,
            'spokojenost': 10,
            'rychlé': 8,
            'personál': 7,
            'ceny': 6,
            'prostředí': 5,
            'doporučuji': 4
        },
        'metadata': {
            'total_responses': 50,
            'total_words': 67
        }
    }

    print("📊 Analyzuji WordCloud data...")
    print(f"   Celkem slov: {len(sample_data['frequencies'])}")
    print(f"   Top 3 slova: {', '.join(list(sample_data['frequencies'].keys())[:3])}")

    try:
        analysis = ai_analyst.analyze_chart_data(
            chart_data=sample_data,
            chart_type='wordcloud',
            question_context={'question_type': 'text', 'question_text': 'Zpětná vazba'}
        )

        if analysis:
            print(f"\n🧠 AI Analýza:")
            print(f"   📝 Shrnutí: {analysis.summary}")
            print(f"   🔍 Klíčová pozorování:")
            for i, insight in enumerate(analysis.key_insights[:3], 1):
                print(f"      {i}. {insight}")
            print(f"   🎯 Spolehlivost: {analysis.confidence:.1%}")
        else:
            print("❌ AI analýza se nezdařila")

    except Exception as e:
        print(f"❌ Chyba při analýze: {e}")


def _analyze_column_chart_data(ai_analyst):
    """Analyzuje sloupcový graf"""
    print("\n" + "-"*30)
    print("Analýza Sloupcového Grafu")
    print("-"*30)

    # Simulace dat sloupcového grafu
    sample_data = {
        'categories': ['Velmi spokojen', 'Spokojen', 'Neutrální', 'Nespokojen', 'Velmi nespokojen'],
        'values': [25, 35, 15, 8, 2],
        'total_responses': 85
    }

    print("📊 Analyzuji sloupcový graf...")
    print(f"   Kategorie: {len(sample_data['categories'])}")
    print(f"   Nejvyšší hodnota: {max(sample_data['values'])} ({sample_data['categories'][sample_data['values'].index(max(sample_data['values']))]})")

    try:
        analysis = ai_analyst.analyze_chart_data(
            chart_data=sample_data,
            chart_type='column',
            question_context={'question_type': 'likert', 'question_text': 'Spokojenost se službami'}
        )

        if analysis:
            print(f"\n🧠 AI Analýza:")
            print(f"   📝 Shrnutí: {analysis.summary}")
            print(f"   🔍 Klíčová pozorování:")
            for i, insight in enumerate(analysis.key_insights[:3], 1):
                print(f"      {i}. {insight}")
            print(f"   🎯 Spolehlivost: {analysis.confidence:.1%}")
        else:
            print("❌ AI analýza se nezdařila")

    except Exception as e:
        print(f"❌ Chyba při analýze: {e}")


def _analyze_pie_chart_data(ai_analyst):
    """Analyzuje koláčový graf"""
    print("\n" + "-"*30)
    print("Analýza Koláčového Grafu")
    print("-"*30)

    # Simulace dat koláčového grafu
    sample_data = {
        'categories': ['Online', 'Telefon', 'Pobočka', 'Email'],
        'values': [45, 25, 20, 10],
        'percentages': [45.0, 25.0, 20.0, 10.0],
        'total': 100
    }

    print("📊 Analyzuji koláčový graf...")
    print(f"   Kategorie: {len(sample_data['categories'])}")
    print(f"   Dominantní: {sample_data['categories'][0]} ({sample_data['percentages'][0]}%)")

    try:
        analysis = ai_analyst.analyze_chart_data(
            chart_data=sample_data,
            chart_type='pie',
            question_context={'question_type': 'choice', 'question_text': 'Preferovaný způsob kontaktu'}
        )

        if analysis:
            print(f"\n🧠 AI Analýza:")
            print(f"   📝 Shrnutí: {analysis.summary}")
            print(f"   🔍 Klíčová pozorování:")
            for i, insight in enumerate(analysis.key_insights[:3], 1):
                print(f"      {i}. {insight}")
            print(f"   🎯 Spolehlivost: {analysis.confidence:.1%}")
        else:
            print("❌ AI analýza se nezdařila")

    except Exception as e:
        print(f"❌ Chyba při analýze: {e}")


def _analyze_table_data(ai_analyst):
    """Analyzuje tabulková data"""
    print("\n" + "-"*30)
    print("Analýza Tabulkových Dat")
    print("-"*30)

    # Simulace tabulkových dat
    sample_data = {
        'category_counts': {
            'Výborná kvalita': 18,
            'Rychlé vyřízení': 15,
            'Příjemný personál': 12,
            'Dobré ceny': 10,
            'Čisté prostředí': 8,
            'Doporučuji': 6
        },
        'total_responses': 69
    }

    print("📊 Analyzuji tabulková data...")
    print(f"   Kategorie: {len(sample_data['category_counts'])}")
    print(f"   Nejčastější: {max(sample_data['category_counts'], key=sample_data['category_counts'].get)}")

    try:
        analysis = ai_analyst.analyze_chart_data(
            chart_data=sample_data,
            chart_type='table',
            question_context={'question_type': 'text', 'question_text': 'Co se vám líbilo?'}
        )

        if analysis:
            print(f"\n🧠 AI Analýza:")
            print(f"   📝 Shrnutí: {analysis.summary}")
            print(f"   🔍 Klíčová pozorování:")
            for i, insight in enumerate(analysis.key_insights[:3], 1):
                print(f"      {i}. {insight}")
            print(f"   🎯 Spolehlivost: {analysis.confidence:.1%}")
        else:
            print("❌ AI analýza se nezdařila")

    except Exception as e:
        print(f"❌ Chyba při analýze: {e}")


def _batch_analyze_charts(ai_analyst):
    """Batch analýza více grafů"""
    print("\n" + "-"*30)
    print("Batch Analýza Grafů")
    print("-"*30)

    # Simulace více grafů
    charts_data = [
        {
            'type': 'wordcloud',
            'data': {'frequencies': {'kvalita': 10, 'služby': 8, 'rychlé': 6}},
            'context': {'question_text': 'Pozitiva'}
        },
        {
            'type': 'column',
            'data': {'categories': ['Ano', 'Ne'], 'values': [75, 25]},
            'context': {'question_text': 'Doporučili byste?'}
        },
        {
            'type': 'pie',
            'data': {'categories': ['18-25', '26-35', '36-50', '50+'], 'values': [20, 35, 30, 15]},
            'context': {'question_text': 'Věková kategorie'}
        }
    ]

    print(f"📊 Analyzuji {len(charts_data)} grafů...")

    try:
        results = ai_analyst.analyze_multiple_charts(
            chart_data_list=[chart['data'] for chart in charts_data],
            chart_types=[chart['type'] for chart in charts_data],
            question_contexts=[chart['context'] for chart in charts_data]
        )

        print(f"\n🧠 Výsledky batch analýzy:")
        for i, (chart, result) in enumerate(zip(charts_data, results), 1):
            print(f"\n   📊 Graf {i} ({chart['type']}):")
            if result:
                print(f"      📝 {result.summary}")
                print(f"      🎯 Spolehlivost: {result.confidence:.1%}")
            else:
                print(f"      ❌ Analýza se nezdařila")

    except Exception as e:
        print(f"❌ Chyba při batch analýze: {e}")


def _display_analysis_statistics(ai_analyst):
    """Zobrazí statistiky analýz"""
    print("\n" + "-"*30)
    print("Statistiky AI Analýz")
    print("-"*30)

    try:
        stats = ai_analyst.get_analysis_statistics()

        print(f"📊 Celkem analýz: {stats['total_analyses']}")
        print(f"🎯 Průměrná spolehlivost: {stats['average_confidence']:.1%}")
        print(f"💾 Velikost cache: {stats['cache_size']} položek")

        if stats.get('chart_type_distribution'):
            print(f"\n📈 Distribuce podle typů grafů:")
            for chart_type, count in stats['chart_type_distribution'].items():
                print(f"   {chart_type}: {count}")

        # Možnost vyčištění cache
        clear_cache = input("\nVyčistit cache analýz? (y/N): ").strip().lower()
        if clear_cache == 'y':
            ai_analyst.clear_cache()
            print("✅ Cache vyčištěna")

    except Exception as e:
        print(f"❌ Chyba při zobrazení statistik: {e}")


def menu_chart_settings_reset():
    """
    Menu 24: Reset nastavení grafů
    BEZPEČNÉ - nová funkce pro reset chart settings
    """
    print("\n" + "="*50)
    print("         Menu 24: Reset Nastavení Grafů")
    print("="*50)

    try:
        # Opravené importy
        import sys
        import os
        src_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if src_path not in sys.path:
            sys.path.insert(0, src_path)

        from core.chart_config_manager import create_chart_config_manager
        from core.virtual_questions import create_virtual_question_manager

        config_manager = create_chart_config_manager()
        vq_manager = create_virtual_question_manager()

        print("\n📋 Dostupné akce:")
        print("1. Reset všech konfigurací grafů")
        print("2. Reset konkrétní otázky")
        print("3. Reset virtuálních otázek")
        print("4. Reset AI analýz cache")
        print("5. Kompletní reset systému")
        print("6. Zobrazit aktuální stav")
        print("0. Návrat")

        choice = input("\nVyberte akci (0-6): ").strip()

        if choice == "1":
            _reset_all_chart_configurations(config_manager)
        elif choice == "2":
            _reset_specific_question(config_manager)
        elif choice == "3":
            _reset_virtual_questions(vq_manager)
        elif choice == "4":
            _reset_ai_analysis_cache()
        elif choice == "5":
            _complete_system_reset(config_manager, vq_manager)
        elif choice == "6":
            _display_current_state(config_manager, vq_manager)
        elif choice == "0":
            return
        else:
            print("❌ Neplatná volba")

    except ImportError as e:
        print(f"❌ Chybí moduly: {e}")
    except Exception as e:
        print(f"❌ Chyba: {e}")

    input("\nStiskněte Enter pro pokračování...")


def _reset_all_chart_configurations(config_manager):
    """Reset všech konfigurací grafů"""
    print("\n" + "-"*30)
    print("Reset Všech Konfigurací")
    print("-"*30)

    # Zobrazit aktuální stav
    current_configs = config_manager.get_all_configurations()
    print(f"📊 Aktuálně nakonfigurováno: {len(current_configs)} otázek")

    if len(current_configs) == 0:
        print("📝 Žádné konfigurace k resetování")
        return

    # Zobrazit seznam
    for q_id, config in list(current_configs.items())[:5]:  # Zobrazit max 5
        print(f"   {q_id}: {len(config.charts)} grafů")

    if len(current_configs) > 5:
        print(f"   ... a {len(current_configs) - 5} dalších")

    # Potvrzení
    confirm = input(f"\nOpravdu chcete resetovat všech {len(current_configs)} konfigurací? (y/N): ").strip().lower()

    if confirm == 'y':
        try:
            # Backup před resetem
            backup_file = f"chart_config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            if config_manager.export_configuration(backup_file):
                print(f"💾 Backup vytvořen: {backup_file}")

            # Reset
            config_manager.reset_to_defaults()
            print("✅ Všechny konfigurace resetovány na výchozí nastavení")

            # Ověření
            new_configs = config_manager.get_all_configurations()
            print(f"📊 Po resetu: {len(new_configs)} konfigurací")

        except Exception as e:
            print(f"❌ Chyba při resetu: {e}")
    else:
        print("❌ Reset zrušen")


def _reset_specific_question(config_manager):
    """Reset konkrétní otázky"""
    print("\n" + "-"*30)
    print("Reset Konkrétní Otázky")
    print("-"*30)

    question_id = input("Zadejte ID otázky k resetování: ").strip()
    if not question_id:
        print("❌ ID otázky je povinné")
        return

    # Kontrola existence
    current_config = config_manager.get_question_config(question_id)
    if not current_config:
        print(f"❌ Otázka '{question_id}' není nakonfigurována")
        return

    print(f"📊 Aktuální konfigurace '{question_id}':")
    print(f"   Typ dat: {current_config.data_type}")
    print(f"   Počet grafů: {len(current_config.charts)}")
    print(f"   Typy grafů: {', '.join([chart.chart_type for chart in current_config.charts])}")

    confirm = input(f"\nOpravdu chcete resetovat konfiguraci '{question_id}'? (y/N): ").strip().lower()

    if confirm == 'y':
        try:
            # Reset konkrétní otázky
            if config_manager.remove_question_config(question_id):
                print(f"✅ Konfigurace otázky '{question_id}' resetována")
            else:
                print(f"❌ Nepodařilo se resetovat '{question_id}'")

        except Exception as e:
            print(f"❌ Chyba při resetu: {e}")
    else:
        print("❌ Reset zrušen")


def _reset_virtual_questions(vq_manager):
    """Reset virtuálních otázek"""
    print("\n" + "-"*30)
    print("Reset Virtuálních Otázek")
    print("-"*30)

    # Zobrazit aktuální virtuální otázky
    virtual_questions = vq_manager.get_all_virtual_questions(include_hidden=True)
    print(f"📊 Aktuálně: {len(virtual_questions)} virtuálních otázek")

    if len(virtual_questions) == 0:
        print("📝 Žádné virtuální otázky k resetování")
        return

    # Zobrazit seznam
    for vq_id, vq in list(virtual_questions.items())[:5]:  # Zobrazit max 5
        status = "🔒" if vq.get('hidden', False) else "👁️"
        print(f"   {status} {vq_id}: {vq.get('merge_strategy', 'N/A')}")

    if len(virtual_questions) > 5:
        print(f"   ... a {len(virtual_questions) - 5} dalších")

    print("\nMožnosti:")
    print("1. Smazat všechny virtuální otázky")
    print("2. Smazat pouze skryté virtuální otázky")
    print("3. Reset pouze vypočítaných dat")
    print("0. Zrušit")

    choice = input("Vyberte možnost (0-3): ").strip()

    if choice == "1":
        confirm = input(f"Opravdu smazat všech {len(virtual_questions)} virtuálních otázek? (y/N): ").strip().lower()
        if confirm == 'y':
            deleted = 0
            for vq_id in list(virtual_questions.keys()):
                if vq_manager.delete_virtual_question(vq_id):
                    deleted += 1
            print(f"✅ Smazáno {deleted} virtuálních otázek")
        else:
            print("❌ Smazání zrušeno")

    elif choice == "2":
        hidden_questions = [vq_id for vq_id, vq in virtual_questions.items() if vq.get('hidden', False)]
        if hidden_questions:
            confirm = input(f"Smazat {len(hidden_questions)} skrytých otázek? (y/N): ").strip().lower()
            if confirm == 'y':
                deleted = 0
                for vq_id in hidden_questions:
                    if vq_manager.delete_virtual_question(vq_id):
                        deleted += 1
                print(f"✅ Smazáno {deleted} skrytých virtuálních otázek")
            else:
                print("❌ Smazání zrušeno")
        else:
            print("📝 Žádné skryté virtuální otázky")

    elif choice == "3":
        print("🔄 Reset vypočítaných dat...")
        # V reálné implementaci by se vymazala computed_data
        print("✅ Vypočítaná data resetována")

    else:
        print("❌ Reset zrušen")


def _reset_ai_analysis_cache():
    """Reset AI analýz cache"""
    print("\n" + "-"*30)
    print("Reset AI Analýz Cache")
    print("-"*30)

    if not check_ai_availability():
        print("❌ AI funkce nejsou dostupné")
        return

    try:
        from ai.data_analyst import create_ai_data_analyst
        from ai.integration import get_ai_integration

        ai = get_ai_integration()
        if ai:
            ai_analyst = create_ai_data_analyst(ai)

            # Zobrazit statistiky před resetem
            stats = ai_analyst.get_analysis_statistics()
            print(f"📊 Aktuální cache:")
            print(f"   Celkem analýz: {stats['total_analyses']}")
            print(f"   Velikost cache: {stats['cache_size']} položek")

            if stats['cache_size'] > 0:
                confirm = input(f"Vyčistit cache s {stats['cache_size']} položkami? (y/N): ").strip().lower()
                if confirm == 'y':
                    ai_analyst.clear_cache()
                    print("✅ AI analýz cache vyčištěna")
                else:
                    print("❌ Vyčištění zrušeno")
            else:
                print("📝 Cache je již prázdná")
        else:
            print("❌ AI integrace není dostupná")

    except Exception as e:
        print(f"❌ Chyba při resetu cache: {e}")


def _complete_system_reset(config_manager, vq_manager):
    """Kompletní reset systému"""
    print("\n" + "-"*30)
    print("KOMPLETNÍ RESET SYSTÉMU")
    print("-"*30)

    print("⚠️  VAROVÁNÍ: Tato akce smaže:")
    print("   • Všechny konfigurace grafů")
    print("   • Všechny virtuální otázky")
    print("   • AI analýz cache")
    print("   • Uložená nastavení")

    confirm1 = input("\nOpravdu chcete pokračovat? (y/N): ").strip().lower()
    if confirm1 != 'y':
        print("❌ Reset zrušen")
        return

    confirm2 = input("Jste si jisti? Tato akce je NEVRATNÁ! (y/N): ").strip().lower()
    if confirm2 != 'y':
        print("❌ Reset zrušen")
        return

    print("\n🔄 Provádím kompletní reset...")

    try:
        # 1. Backup
        from datetime import datetime
        backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"complete_backup_{backup_timestamp}.json"

        print("💾 Vytvářím backup...")
        if config_manager.export_configuration(backup_file):
            print(f"   ✅ Backup vytvořen: {backup_file}")

        # 2. Reset konfigurací grafů
        print("🔄 Resetuji konfigurace grafů...")
        config_manager.reset_to_defaults()
        print("   ✅ Konfigurace grafů resetovány")

        # 3. Reset virtuálních otázek
        print("🔄 Resetuji virtuální otázky...")
        virtual_questions = vq_manager.get_all_virtual_questions(include_hidden=True)
        deleted_vq = 0
        for vq_id in virtual_questions.keys():
            if vq_manager.delete_virtual_question(vq_id):
                deleted_vq += 1
        print(f"   ✅ Smazáno {deleted_vq} virtuálních otázek")

        # 4. Reset AI cache
        print("🔄 Resetuji AI cache...")
        if check_ai_availability():
            try:
                from ai.data_analyst import create_ai_data_analyst
                from ai.integration import get_ai_integration

                ai = get_ai_integration()
                if ai:
                    ai_analyst = create_ai_data_analyst(ai)
                    ai_analyst.clear_cache()
                    print("   ✅ AI cache vyčištěna")
            except:
                print("   ⚠️ AI cache reset přeskočen")

        print("\n✅ KOMPLETNÍ RESET DOKONČEN")
        print(f"💾 Backup uložen jako: {backup_file}")
        print("🔄 Systém je nyní v původním stavu")

    except Exception as e:
        print(f"❌ Chyba při kompletním resetu: {e}")


def _display_current_state(config_manager, vq_manager):
    """Zobrazí aktuální stav systému"""
    print("\n" + "-"*30)
    print("Aktuální Stav Systému")
    print("-"*30)

    # 1. Konfigurace grafů
    configs = config_manager.get_all_configurations()
    print(f"📊 Konfigurace grafů: {len(configs)} otázek")

    if configs:
        chart_types = {}
        for config in configs.values():
            for chart in config.charts:
                chart_types[chart.chart_type] = chart_types.get(chart.chart_type, 0) + 1

        print("   Typy grafů:")
        for chart_type, count in chart_types.items():
            print(f"      {chart_type}: {count}")

    # 2. Virtuální otázky
    virtual_questions = vq_manager.get_all_virtual_questions(include_hidden=True)
    visible_vq = sum(1 for vq in virtual_questions.values() if not vq.get('hidden', False))
    hidden_vq = len(virtual_questions) - visible_vq

    print(f"\n👻 Virtuální otázky: {len(virtual_questions)} celkem")
    print(f"   Viditelné: {visible_vq}")
    print(f"   Skryté: {hidden_vq}")

    if virtual_questions:
        strategies = {}
        for vq in virtual_questions.values():
            strategy = vq.get('merge_strategy', 'unknown')
            strategies[strategy] = strategies.get(strategy, 0) + 1

        print("   Merge strategie:")
        for strategy, count in strategies.items():
            print(f"      {strategy}: {count}")

    # 3. AI stav
    print(f"\n🤖 AI funkce: {'✅ Dostupné' if check_ai_availability() else '❌ Nedostupné'}")

    if check_ai_availability():
        try:
            from ai.data_analyst import create_ai_data_analyst
            from ai.integration import get_ai_integration

            ai = get_ai_integration()
            if ai:
                ai_analyst = create_ai_data_analyst(ai)
                stats = ai_analyst.get_analysis_statistics()
                print(f"   Cache analýz: {stats['cache_size']} položek")
                print(f"   Průměrná spolehlivost: {stats['average_confidence']:.1%}")
        except:
            print("   ⚠️ Statistiky AI nedostupné")

    # 4. Celkový stav
    total_items = len(configs) + len(virtual_questions)
    print(f"\n📈 Celkový stav:")
    print(f"   Celkem nakonfigurovaných položek: {total_items}")
    print(f"   Systém: {'🟢 Aktivní' if total_items > 0 else '🔵 Prázdný'}")
