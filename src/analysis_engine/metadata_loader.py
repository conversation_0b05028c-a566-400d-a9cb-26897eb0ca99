"""
Metadata Loader

Načítá a spravuje metadata o typech otázek, analýz a vizualizací
z nedokončeného projektu.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class QuestionType:
    """Typ otázky z LimeSurvey"""
    id: str
    code: str
    name: str
    description: str
    limesurvey_type: str
    priority: int
    supported_analyses: List[str]


@dataclass
class AnalysisType:
    """Typ analýzy"""
    id: str
    code: str
    name: str
    description: str
    detailed_description: str
    analysis_type: str  # 'question' nebo 'section'
    priority: int
    supported_visualizations: List[str]
    supported_question_types: List[str]


@dataclass
class VisualizationType:
    """Typ vizualizace"""
    id: str
    code: str
    name: str
    description: str
    priority: int
    datawrapper_type: Optional[str] = None


class MetadataLoader:
    """Načítá metadata z nedokončeného projektu"""
    
    def __init__(self, metadata_dir: Optional[Path] = None):
        """
        Inicializace metadata loaderu
        
        Args:
            metadata_dir: Cesta k adresáři s metadata (volitelné)
        """
        if metadata_dir is None:
            # Default cesta k nedokončenému projektu
            metadata_dir = Path(__file__).parent.parent.parent / "tmp" / "limesurvey-structure-metadata"
        
        self.metadata_dir = Path(metadata_dir)
        
        # Data stores
        self.question_types: Dict[str, QuestionType] = {}
        self.analysis_types: Dict[str, AnalysisType] = {}
        self.visualization_types: Dict[str, VisualizationType] = {}
        self.question_to_analyses: Dict[str, List[str]] = {}
        
        logger.info(f"MetadataLoader inicializován s adresářem: {self.metadata_dir}")
    
    def load_all_metadata(self) -> bool:
        """Načte všechna metadata"""
        try:
            self._load_question_types()
            self._load_analysis_types()
            self._load_visualization_types()
            self._load_question_mappings()
            
            logger.info(f"Načteno metadata:")
            logger.info(f"  - Typy otázek: {len(self.question_types)}")
            logger.info(f"  - Typy analýz: {len(self.analysis_types)}")
            logger.info(f"  - Typy vizualizací: {len(self.visualization_types)}")
            logger.info(f"  - Mapování otázek: {len(self.question_to_analyses)}")
            
            return True
            
        except Exception as e:
            logger.error(f"Chyba při načítání metadata: {str(e)}")
            return False
    
    def _load_question_types(self):
        """Načte typy otázek z otazky.md"""
        file_path = self.metadata_dir / "otazky.md"
        
        if not file_path.exists():
            logger.warning(f"Soubor {file_path} neexistuje")
            return
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parsování markdown tabulky
        lines = content.strip().split('\n')
        
        # Najdeme začátek tabulky
        table_start = None
        for i, line in enumerate(lines):
            if line.strip().startswith('| ID ') or line.strip().startswith('| ID|'):
                table_start = i + 2  # Přeskočíme header a separator
                break
        
        if table_start is None:
            logger.warning("Tabulka typů otázek nenalezena")
            return
        
        # Parsování řádků tabulky
        for line in lines[table_start:]:
            line = line.strip()
            if not line or not line.startswith('|'):
                continue
            
            parts = [part.strip() for part in line.split('|')[1:-1]]
            if len(parts) >= 3:
                # Formát: ID | Název | Popis
                question_id = parts[0]
                question_name = parts[1]
                question_desc = parts[2]

                question_type = QuestionType(
                    id=question_id,
                    code=question_id,  # ID a code jsou stejné
                    name=question_name,
                    description=question_desc,
                    limesurvey_type=question_id,  # LimeSurvey typ je ID
                    priority=3,  # Default priorita
                    supported_analyses=[]
                )
                self.question_types[question_id] = question_type
        
        logger.info(f"Načteno {len(self.question_types)} typů otázek")
    
    def _load_analysis_types(self):
        """Načte typy analýz z analyzy-otazky.md a analyzy-sekce.md"""
        
        # Načtení analýz otázek
        self._load_analysis_file("analyzy-otazky.md", "question")
        
        # Načtení analýz sekcí
        self._load_analysis_file("analyzy-sekce.md", "section")
    
    def _load_analysis_file(self, filename: str, analysis_type: str):
        """Načte analýzy z konkrétního souboru"""
        file_path = self.metadata_dir / filename
        
        if not file_path.exists():
            logger.warning(f"Soubor {file_path} neexistuje")
            return
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parsování markdown tabulky
        lines = content.strip().split('\n')
        
        # Najdeme začátek tabulky
        table_start = None
        for i, line in enumerate(lines):
            if line.strip().startswith('| ID '):
                table_start = i + 2
                break
        
        if table_start is None:
            return
        
        # Parsování řádků tabulky
        for line in lines[table_start:]:
            line = line.strip()
            if not line or not line.startswith('|'):
                continue
            
            parts = [part.strip() for part in line.split('|')[1:-1]]
            if len(parts) >= 4:
                # Parsování vizualizací (oddělené čárkami)
                visualizations = []
                if len(parts) > 4 and parts[4]:
                    visualizations = [v.strip() for v in parts[4].split(',')]
                
                analysis = AnalysisType(
                    id=parts[0],
                    code=parts[1],
                    name=parts[2],
                    description=parts[3],
                    detailed_description="",  # Načteme později z JSON
                    analysis_type=analysis_type,
                    priority=int(parts[5]) if len(parts) > 5 and parts[5].isdigit() else 3,
                    supported_visualizations=visualizations,
                    supported_question_types=[]
                )
                self.analysis_types[parts[0]] = analysis
        
        logger.info(f"Načteno {len([a for a in self.analysis_types.values() if a.analysis_type == analysis_type])} analýz typu {analysis_type}")
    
    def _load_visualization_types(self):
        """Načte typy vizualizací z vizualizace.md"""
        file_path = self.metadata_dir / "vizualizace.md"
        
        if not file_path.exists():
            logger.warning(f"Soubor {file_path} neexistuje")
            return
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parsování markdown tabulky
        lines = content.strip().split('\n')
        
        # Najdeme začátek tabulky
        table_start = None
        for i, line in enumerate(lines):
            if line.strip().startswith('| ID '):
                table_start = i + 2
                break
        
        if table_start is None:
            return
        
        # Parsování řádků tabulky
        for line in lines[table_start:]:
            line = line.strip()
            if not line or not line.startswith('|'):
                continue
            
            parts = [part.strip() for part in line.split('|')[1:-1]]
            if len(parts) >= 4:
                # Mapování na Datawrapper typy
                datawrapper_mapping = {
                    'BAR': 'd3-bars',
                    'HBA': 'd3-bars',
                    'PIE': 'd3-pies',
                    'TAB': 'tables',
                    'HIS': 'd3-bars',
                    'BOX': 'd3-scatter-plot',
                    'SCP': 'd3-scatter-plot'
                }
                
                viz_type = VisualizationType(
                    id=parts[0],
                    code=parts[0],  # ID a code jsou stejné
                    name=parts[1],
                    description=parts[2],
                    priority=int(parts[3]) if parts[3].isdigit() else 3,
                    datawrapper_type=datawrapper_mapping.get(parts[0])
                )
                self.visualization_types[parts[0]] = viz_type
        
        logger.info(f"Načteno {len(self.visualization_types)} typů vizualizací")
    
    def _load_question_mappings(self):
        """Načte mapování otázek na analýzy z otazky2analyzy.md"""
        file_path = self.metadata_dir / "otazky2analyzy.md"
        
        if not file_path.exists():
            logger.warning(f"Soubor {file_path} neexistuje")
            return
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parsování markdown tabulky
        lines = content.strip().split('\n')
        
        # Najdeme začátek tabulky
        table_start = None
        for i, line in enumerate(lines):
            if line.strip().startswith('| Question ID ') or line.strip().startswith('| Question ID|'):
                table_start = i + 2
                break
        
        if table_start is None:
            return
        
        # Parsování řádků tabulky
        for line in lines[table_start:]:
            line = line.strip()
            if not line or not line.startswith('|'):
                continue
            
            parts = [part.strip() for part in line.split('|')[1:-1]]
            if len(parts) >= 3:
                question_id = parts[0]
                analysis_id = parts[1]
                viz_ids_str = parts[2]

                # Parsování vizualizací (oddělené čárkami)
                viz_ids = []
                if viz_ids_str:
                    viz_ids = [v.strip() for v in viz_ids_str.split(',')]

                # Přidání mapování question → analysis
                if question_id not in self.question_to_analyses:
                    self.question_to_analyses[question_id] = []
                self.question_to_analyses[question_id].append(analysis_id)

                # Aktualizace supported_analyses v question_types
                if question_id in self.question_types:
                    if analysis_id not in self.question_types[question_id].supported_analyses:
                        self.question_types[question_id].supported_analyses.append(analysis_id)

                # Aktualizace supported_question_types a vizualizací v analysis_types
                if analysis_id in self.analysis_types:
                    if question_id not in self.analysis_types[analysis_id].supported_question_types:
                        self.analysis_types[analysis_id].supported_question_types.append(question_id)

                    # Aktualizace podporovaných vizualizací
                    self.analysis_types[analysis_id].supported_visualizations = viz_ids
        
        logger.info(f"Načteno mapování pro {len(self.question_to_analyses)} typů otázek")
    
    def get_analyses_for_question(self, question_type_id: str) -> List[AnalysisType]:
        """Vrátí doporučené analýzy pro typ otázky"""
        analysis_ids = self.question_to_analyses.get(question_type_id, [])
        return [self.analysis_types[aid] for aid in analysis_ids if aid in self.analysis_types]
    
    def get_visualizations_for_analysis(self, analysis_id: str) -> List[VisualizationType]:
        """Vrátí doporučené vizualizace pro analýzu"""
        if analysis_id not in self.analysis_types:
            return []
        
        viz_ids = self.analysis_types[analysis_id].supported_visualizations
        return [self.visualization_types[vid] for vid in viz_ids if vid in self.visualization_types]
    
    def get_high_priority_items(self, priority_threshold: int = 3) -> Dict[str, List]:
        """Vrátí položky s vysokou prioritou (≤ threshold)"""
        return {
            'question_types': [qt for qt in self.question_types.values() if qt.priority <= priority_threshold],
            'analysis_types': [at for at in self.analysis_types.values() if at.priority <= priority_threshold],
            'visualization_types': [vt for vt in self.visualization_types.values() if vt.priority <= priority_threshold]
        }
