"""
Intelligent Analysis Module

Menu 13 - Inteligentní analýza průzkumu
Využívá Analysis Engine pro automatické doporučování analýz a vizualizací.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import asdict

from analysis_engine import <PERSON><PERSON><PERSON><PERSON><PERSON>der, QuestionAnalyzer, AnalysisRecommender, VisualizationMapper
from analysis_engine.question_analyzer import LimeSurveyQuestion, AnalyzedQuestion
from analysis_engine.analysis_recommender import SurveyAnalysisPlan
from analysis_engine.visualization_mapper import ChartGenerationPlan

from limesurvey_client import LimeSurveyClient

logger = logging.getLogger(__name__)


class IntelligentAnalysisManager:
    """Správce inteligentní analýzy průzkumů"""
    
    def __init__(self):
        """Inicializace manageru"""
        self.metadata_loader = MetadataLoader()
        self.question_analyzer = None
        self.analysis_recommender = None
        self.visualization_mapper = None
        
        # Načtení metadata
        self._load_metadata()
        
        logger.info("IntelligentAnalysisManager inicializován")
    
    def _load_metadata(self) -> bool:
        """Načte metadata Analysis Engine"""
        try:
            success = self.metadata_loader.load_all_metadata()
            
            if success:
                # Inicializace komponent
                self.question_analyzer = QuestionAnalyzer(self.metadata_loader)
                self.analysis_recommender = AnalysisRecommender(self.metadata_loader)
                self.visualization_mapper = VisualizationMapper(self.metadata_loader)
                
                logger.info(f"✅ Metadata načtena:")
                logger.info(f"   - Typy otázek: {len(self.metadata_loader.question_types)}")
                logger.info(f"   - Typy analýz: {len(self.metadata_loader.analysis_types)}")
                logger.info(f"   - Typy vizualizací: {len(self.metadata_loader.visualization_types)}")
                
                return True
            else:
                logger.error("❌ Načítání metadata selhalo")
                return False
                
        except Exception as e:
            logger.error(f"❌ Chyba při načítání metadata: {str(e)}")
            return False
    
    def analyze_survey_from_lss(self, lss_file_path: str) -> Optional[Dict]:
        """
        Analyzuje průzkum z LSS souboru
        
        Args:
            lss_file_path: Cesta k LSS souboru
            
        Returns:
            Slovník s kompletní analýzou nebo None při chybě
        """
        try:
            logger.info(f"🔍 Analyzuji průzkum z LSS: {lss_file_path}")
            
            # 1. Načtení struktury z LSS
            questions = self._extract_questions_from_lss(lss_file_path)
            
            if not questions:
                logger.error("❌ Nepodařilo se načíst otázky z LSS souboru")
                return None
            
            logger.info(f"📊 Načteno {len(questions)} otázek")
            
            # 2. Analýza otázek
            analyzed_questions = self.question_analyzer.analyze_survey_questions(questions)
            
            # 3. Doporučení analýz
            analysis_plan = self.analysis_recommender.recommend_analyses(analyzed_questions)
            
            # 4. Mapování vizualizací
            all_analyses = (analysis_plan.question_level_analyses + 
                           analysis_plan.section_level_analyses + 
                           analysis_plan.cross_question_analyses)
            
            chart_plan = self.visualization_mapper.map_analyses_to_visualizations(all_analyses)
            
            # 5. Sestavení výsledku
            result = {
                'survey_info': {
                    'lss_file': lss_file_path,
                    'total_questions': len(questions),
                    'analyzed_questions': len(analyzed_questions),
                    'recognition_rate': len([q for q in analyzed_questions if q.matched_type]) / len(analyzed_questions)
                },
                'question_analysis': self._format_question_analysis(analyzed_questions),
                'analysis_plan': self._format_analysis_plan(analysis_plan),
                'chart_plan': self._format_chart_plan(chart_plan),
                'summary': self._generate_summary(analyzed_questions, analysis_plan, chart_plan)
            }
            
            logger.info("✅ Analýza průzkumu dokončena")
            return result
            
        except Exception as e:
            logger.error(f"❌ Chyba při analýze průzkumu: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def _extract_questions_from_lss(self, lss_file_path: str) -> List[LimeSurveyQuestion]:
        """Extrahuje otázky z LSS souboru"""
        try:
            # Import zde kvůli circular imports
            from lss_parser import LSSParser
            
            parser = LSSParser()
            survey_data = parser.parse_lss_file(lss_file_path)
            
            if not survey_data or 'questions' not in survey_data:
                logger.error("❌ LSS soubor neobsahuje validní data otázek")
                return []
            
            questions = []
            
            for question_data in survey_data['questions']:
                # Extrakce základních informací
                qid = str(question_data.get('qid', ''))
                question_code = question_data.get('title', f'Q{qid}')
                question_text = question_data.get('question', '')
                question_type = question_data.get('type', 'L')
                
                # Extrakce podotázek
                subquestions = []
                if 'subquestions' in question_data:
                    for sq in question_data['subquestions']:
                        subquestions.append({
                            'code': sq.get('title', ''),
                            'text': sq.get('question', '')
                        })
                
                # Extrakce možností odpovědí
                answer_options = []
                if 'answeroptions' in question_data:
                    for ao in question_data['answeroptions']:
                        answer_options.append({
                            'code': ao.get('code', ''),
                            'text': ao.get('answer', '')
                        })
                
                # Extrakce atributů
                attributes = question_data.get('attributes', {})
                
                question = LimeSurveyQuestion(
                    qid=qid,
                    question_code=question_code,
                    question_text=question_text,
                    question_type=question_type,
                    subquestions=subquestions if subquestions else None,
                    answer_options=answer_options if answer_options else None,
                    attributes=attributes if attributes else None
                )
                
                questions.append(question)
            
            logger.info(f"✅ Extrahováno {len(questions)} otázek z LSS")
            return questions
            
        except Exception as e:
            logger.error(f"❌ Chyba při extrakci otázek z LSS: {str(e)}")
            return []
    
    def _format_question_analysis(self, analyzed_questions: List[AnalyzedQuestion]) -> Dict:
        """Formátuje analýzu otázek pro výstup"""
        summary = self.question_analyzer.get_analysis_summary(analyzed_questions)
        
        questions_detail = []
        for q in analyzed_questions:
            questions_detail.append({
                'qid': q.original_question.qid,
                'question_code': q.original_question.question_code,
                'question_text': q.original_question.question_text[:100] + '...' if len(q.original_question.question_text) > 100 else q.original_question.question_text,
                'limesurvey_type': q.original_question.question_type,
                'matched_type': {
                    'id': q.matched_type.id,
                    'name': q.matched_type.name,
                    'description': q.matched_type.description
                } if q.matched_type else None,
                'confidence': q.confidence,
                'recommended_analyses': q.recommended_analyses,
                'notes': q.analysis_notes
            })
        
        return {
            'summary': summary,
            'questions': questions_detail
        }
    
    def _format_analysis_plan(self, analysis_plan: SurveyAnalysisPlan) -> Dict:
        """Formátuje plán analýz pro výstup"""
        def format_recommendations(recommendations):
            return [{
                'analysis_id': rec.analysis_type.id,
                'analysis_name': rec.analysis_type.name,
                'analysis_description': rec.analysis_type.description,
                'applicable_questions': rec.applicable_questions,
                'priority_score': rec.priority_score,
                'estimated_complexity': rec.estimated_complexity,
                'reasoning': rec.reasoning,
                'prerequisites': rec.prerequisites
            } for rec in recommendations]
        
        return {
            'question_level_analyses': format_recommendations(analysis_plan.question_level_analyses),
            'section_level_analyses': format_recommendations(analysis_plan.section_level_analyses),
            'cross_question_analyses': format_recommendations(analysis_plan.cross_question_analyses),
            'total_estimated_time': analysis_plan.total_estimated_time,
            'complexity_score': analysis_plan.complexity_score
        }
    
    def _format_chart_plan(self, chart_plan: ChartGenerationPlan) -> Dict:
        """Formátuje plán grafů pro výstup"""
        def format_visualizations(visualizations):
            return [{
                'visualization_id': viz.visualization_type.id,
                'visualization_name': viz.visualization_type.name,
                'visualization_description': viz.visualization_type.description,
                'analysis_id': viz.analysis_id,
                'datawrapper_type': viz.datawrapper_type,
                'priority_score': viz.priority_score,
                'data_requirements': viz.data_requirements,
                'configuration_hints': viz.configuration_hints,
                'fallback_options': viz.fallback_options
            } for viz in visualizations]
        
        return {
            'primary_visualizations': format_visualizations(chart_plan.primary_visualizations),
            'alternative_visualizations': format_visualizations(chart_plan.alternative_visualizations),
            'unsupported_analyses': chart_plan.unsupported_analyses,
            'total_charts_estimate': chart_plan.total_charts_estimate,
            'datawrapper_compatibility': chart_plan.datawrapper_compatibility
        }
    
    def _generate_summary(self, analyzed_questions: List[AnalyzedQuestion], 
                         analysis_plan: SurveyAnalysisPlan, 
                         chart_plan: ChartGenerationPlan) -> Dict:
        """Generuje souhrn analýzy"""
        total_analyses = (len(analysis_plan.question_level_analyses) + 
                         len(analysis_plan.section_level_analyses) + 
                         len(analysis_plan.cross_question_analyses))
        
        recognized_questions = len([q for q in analyzed_questions if q.matched_type])
        
        return {
            'total_questions': len(analyzed_questions),
            'recognized_questions': recognized_questions,
            'recognition_rate': recognized_questions / len(analyzed_questions) if analyzed_questions else 0,
            'total_analyses': total_analyses,
            'total_visualizations': len(chart_plan.primary_visualizations),
            'estimated_time_hours': analysis_plan.total_estimated_time / 60,
            'complexity_score': analysis_plan.complexity_score,
            'datawrapper_compatibility': chart_plan.datawrapper_compatibility,
            'implementation_priority': 'Vysoká' if analysis_plan.complexity_score <= 3 else 'Střední' if analysis_plan.complexity_score <= 6 else 'Nízká'
        }
    
    def export_analysis_report(self, analysis_result: Dict, output_path: str) -> bool:
        """
        Exportuje analýzu do JSON souboru
        
        Args:
            analysis_result: Výsledek analýzy
            output_path: Cesta k výstupnímu souboru
            
        Returns:
            True při úspěchu
        """
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ Analýza exportována do: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při exportu analýzy: {str(e)}")
            return False
