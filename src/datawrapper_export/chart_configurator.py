"""
Chart Configurator

Konfigurace metadata grafů pro export:
- Locale cs-CZ
- Download options (PNG, PDF, SVG)
- Sharing a social media
- Republish workflow (unpublish → configure → publish)
"""

import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from .datawrapper_client import DatawrapperExportClient, ChartInfo

logger = logging.getLogger(__name__)


@dataclass
class ConfigurationResult:
    """Výsledek konfigurace grafu"""
    chart_id: str
    success: bool
    new_share_url: Optional[str] = None
    error_message: Optional[str] = None


class ChartConfigurator:
    """Konfigurace metadata grafů pro export"""
    
    # Požadovaná metadata pro export
    REQUIRED_CHART_SETTINGS = {
        'publish': {
            'locale': 'cs-CZ',
            # Layout options v Datawrapper UI (blocks struktura)
            'blocks': {
                'embed': True,                   # Embed link v Layout
                'download-image': True,          # Image download options
                'download-pdf': True,            # PDF download
                'download-svg': True,            # SVG download
                'get-the-data': True,            # Data download
                'logo': {'enabled': False}       # Logo (obvykle vypnuto)
            },
            # Chart footer (pro kompatibilitu)
            'chart-footer': {
                'data-download': True,           # Stažení dat
                'image-download': {
                    'png': True,                 # PNG download
                    'pdf': True,                 # PDF download
                    'svg': True                  # SVG download
                },
                'embed-link': True,              # Embed kód
                'social-sharing': True           # Sociální sítě
            }
        },
        # Social media sharing v visualize sekci
        'visualize': {
            'sharing': {
                'enabled': True,                 # Social sharing buttons
                'auto': False,                   # Automatické URL
                'url': 'PLACEHOLDER_SITE_URL'    # URL stránky kde je graf embedován
            }
        }
    }
    
    def __init__(self, client: DatawrapperExportClient):
        self.client = client
        self.configuration_results = []
        
    def configure_charts_for_export(self, charts: List[ChartInfo], 
                                  force_republish: bool = True) -> List[ConfigurationResult]:
        """
        Konfiguruje všechny grafy pro export
        
        Args:
            charts: Seznam grafů ke konfiguraci
            force_republish: Zda vynutit republish i u již publikovaných grafů
            
        Returns:
            Seznam výsledků konfigurace
        """
        logger.info(f"Začínám konfiguraci {len(charts)} grafů pro export")
        
        self.configuration_results = []
        
        for i, chart in enumerate(charts, 1):
            logger.info(f"Konfiguruji graf {i}/{len(charts)}: {chart.title} (ID: {chart.id})")
            
            result = self._configure_single_chart(chart, force_republish)
            self.configuration_results.append(result)
            
            if result.success:
                logger.info(f"✅ Graf {chart.id} úspěšně nakonfigurován")
                if result.new_share_url:
                    chart.share_url = result.new_share_url
            else:
                logger.error(f"❌ Chyba při konfiguraci grafu {chart.id}: {result.error_message}")
        
        # Statistiky
        successful = sum(1 for r in self.configuration_results if r.success)
        logger.info(f"Konfigurace dokončena: {successful}/{len(charts)} grafů úspěšně")
        
        return self.configuration_results
    
    def _configure_single_chart(self, chart: ChartInfo, 
                              force_republish: bool) -> ConfigurationResult:
        """Konfiguruje jeden graf"""
        try:
            # Získáme aktuální metadata
            current_metadata = self.client.get_chart_metadata(chart.id)
            if current_metadata is None:
                return ConfigurationResult(
                    chart_id=chart.id,
                    success=False,
                    error_message="Nepodařilo se načíst metadata grafu"
                )
            
            # Připravíme nová metadata
            new_metadata = self._merge_metadata(current_metadata, self.REQUIRED_CHART_SETTINGS)
            
            # Workflow: unpublish → configure → publish
            success, share_url, error = self._republish_workflow(chart.id, new_metadata, force_republish)
            
            return ConfigurationResult(
                chart_id=chart.id,
                success=success,
                new_share_url=share_url,
                error_message=error
            )
            
        except Exception as e:
            logger.error(f"Exception při konfiguraci grafu {chart.id}: {str(e)}")
            return ConfigurationResult(
                chart_id=chart.id,
                success=False,
                error_message=f"Exception: {str(e)}"
            )
    
    def _merge_metadata(self, current_metadata: Dict, required_settings: Dict) -> Dict:
        """Sloučí aktuální metadata s požadovanými nastaveními"""
        # Vytvoříme kopii aktuálních metadata
        merged = current_metadata.copy()
        
        # Rekurzivně sloučíme požadovaná nastavení
        def deep_merge(target: Dict, source: Dict):
            for key, value in source.items():
                if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                    deep_merge(target[key], value)
                else:
                    target[key] = value
        
        deep_merge(merged, required_settings)
        
        logger.debug(f"Metadata sloučena, nové publish nastavení: {merged.get('publish', {})}")
        return merged
    
    def _republish_workflow(self, chart_id: str, new_metadata: Dict, 
                          force_republish: bool) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Provede republish workflow: unpublish → configure → publish
        
        Returns:
            (success, share_url, error_message)
        """
        try:
            # Krok 1: Unpublish (pokud je potřeba)
            if force_republish:
                logger.debug(f"Unpublishing graf {chart_id}")
                unpublish_success = self.client.unpublish_chart(chart_id)
                if not unpublish_success:
                    logger.warning(f"Unpublish grafu {chart_id} selhal, pokračujem")
            
            # Krok 2: Aktualizace metadata
            logger.debug(f"Aktualizuji metadata grafu {chart_id}")
            metadata_success = self.client.update_chart_metadata(chart_id, new_metadata)
            if not metadata_success:
                return False, None, "Nepodařilo se aktualizovat metadata"
            
            # Krok 3: Publish
            logger.debug(f"Publishing graf {chart_id}")
            share_url = self.client.publish_chart(chart_id)
            if not share_url:
                return False, None, "Nepodařilo se publikovat graf"
            
            return True, share_url, None
            
        except Exception as e:
            error_msg = f"Chyba v republish workflow: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg
    
    def configure_chart_language(self, chart: ChartInfo, language: str = 'cs-CZ') -> bool:
        """Nastaví jazyk grafu"""
        try:
            current_metadata = self.client.get_chart_metadata(chart.id)
            if not current_metadata:
                return False
            
            # Nastavíme locale
            if 'publish' not in current_metadata:
                current_metadata['publish'] = {}
            
            current_metadata['publish']['locale'] = language
            
            # Aktualizujeme metadata
            return self.client.update_chart_metadata(chart.id, current_metadata)
            
        except Exception as e:
            logger.error(f"Chyba při nastavování jazyka grafu {chart.id}: {str(e)}")
            return False
    
    def enable_download_options(self, chart: ChartInfo) -> bool:
        """Povolí všechny download možnosti pro graf"""
        try:
            current_metadata = self.client.get_chart_metadata(chart.id)
            if not current_metadata:
                return False
            
            # Nastavíme download options
            if 'publish' not in current_metadata:
                current_metadata['publish'] = {}
            if 'chart-footer' not in current_metadata['publish']:
                current_metadata['publish']['chart-footer'] = {}
            
            footer = current_metadata['publish']['chart-footer']
            footer['data-download'] = True
            footer['image-download'] = {
                'png': True,
                'pdf': True,
                'svg': True
            }
            footer['embed-link'] = True
            footer['social-sharing'] = True
            
            # Aktualizujeme metadata
            return self.client.update_chart_metadata(chart.id, current_metadata)
            
        except Exception as e:
            logger.error(f"Chyba při povolování download options pro graf {chart.id}: {str(e)}")
            return False
    
    def get_configuration_summary(self) -> Dict:
        """Vrátí souhrn konfigurace"""
        if not self.configuration_results:
            return {"message": "Žádná konfigurace nebyla provedena"}
        
        total = len(self.configuration_results)
        successful = sum(1 for r in self.configuration_results if r.success)
        failed = total - successful
        
        failed_charts = [
            {"chart_id": r.chart_id, "error": r.error_message}
            for r in self.configuration_results if not r.success
        ]
        
        return {
            "total_charts": total,
            "successful": successful,
            "failed": failed,
            "success_rate": f"{(successful/total*100):.1f}%" if total > 0 else "0%",
            "failed_charts": failed_charts
        }
    
    def validate_chart_configuration(self, chart: ChartInfo) -> Dict:
        """Ověří, zda je graf správně nakonfigurován pro export"""
        try:
            metadata = self.client.get_chart_metadata(chart.id)
            if not metadata:
                return {"valid": False, "issues": ["Nepodařilo se načíst metadata"]}

            issues = []
            publish_meta = metadata.get('publish', {})

            # Kontrola locale
            if publish_meta.get('locale') != 'cs-CZ':
                issues.append("Locale není nastaveno na cs-CZ")

            # Kontrola blocks (Layout options v UI)
            blocks = publish_meta.get('blocks', {})
            if not blocks.get('embed'):
                issues.append("Embed link není povolen v Layout options")

            if not blocks.get('download-image'):
                issues.append("Image download není povolen v Layout options")

            if not blocks.get('download-pdf'):
                issues.append("PDF download není povolen v Layout options")

            if not blocks.get('download-svg'):
                issues.append("SVG download není povolen v Layout options")

            if not blocks.get('get-the-data'):
                issues.append("Data download není povolen v Layout options")

            # Kontrola social sharing v visualize sekci
            visualize_meta = metadata.get('visualize', {})
            sharing = visualize_meta.get('sharing', {})
            if not sharing.get('enabled'):
                issues.append("Social sharing není povolen")

            # Kontrola chart-footer (pro kompatibilitu)
            footer = publish_meta.get('chart-footer', {})

            return {
                "valid": len(issues) == 0,
                "issues": issues,
                "current_locale": publish_meta.get('locale', 'not set'),
                "blocks_settings": blocks,
                "chart_footer_settings": footer,
                "sharing_settings": sharing
            }
            
        except Exception as e:
            return {"valid": False, "issues": [f"Chyba při validaci: {str(e)}"]}
