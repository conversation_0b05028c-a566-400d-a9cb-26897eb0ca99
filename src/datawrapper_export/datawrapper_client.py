"""
Datawrapper Export API Client

Specializovaný API klient pro export modul s metodami pro:
- <PERSON><PERSON><PERSON><PERSON><PERSON> a grafů
- Konfigurace metadata grafů
- Publikování/unpublikování grafů
- Error handling a retry mechanismus
"""

import os
import time
import logging
import requests
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ChartInfo:
    """Informace o grafu"""
    id: str
    title: str
    type: str
    status: str
    url: str
    share_url: Optional[str] = None
    metadata: Optional[Dict] = None


class DatawrapperExportClient:
    """API klient pro Datawrapper export modul"""
    
    def __init__(self):
        self.api_url = "https://api.datawrapper.de/v3"
        self.api_key = os.getenv('DATAWRAPPER_API_KEY')
        self.team_id = os.getenv('DATAWRAPPER_TEAM_ID')
        self.limesurvey_folder_id = os.getenv('DATAWRAPPER_LIMESURVEY_FOLDER_ID')
        
        if not self.api_key:
            raise ValueError("DATAWRAPPER_API_KEY není nastaven v .env")
            
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "accept": "*/*",
            "content-type": "application/json"
        }
        
        # Rate limiting settings
        self.rate_limit_delay = 0.5  # seconds between requests
        self.max_retries = 3
        self.retry_delay = 2  # seconds
        
        logger.info(f"Inicializován DatawrapperExportClient (Team: {self.team_id})")

    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Provede HTTP request s error handling a retry mechanimsem"""
        url = f"{self.api_url}/{endpoint.lstrip('/')}"
        
        for attempt in range(self.max_retries):
            try:
                # Rate limiting
                time.sleep(self.rate_limit_delay)
                
                response = requests.request(
                    method=method,
                    url=url,
                    headers=self.headers,
                    timeout=30,
                    **kwargs
                )
                
                # Check for rate limiting
                if response.status_code == 429:
                    wait_time = int(response.headers.get('Retry-After', self.retry_delay * (attempt + 1)))
                    logger.warning(f"Rate limit hit, waiting {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                
                # Success or client error (don't retry)
                if response.status_code < 500:
                    return response
                    
                # Server error - retry
                logger.warning(f"Server error {response.status_code}, attempt {attempt + 1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
                    
            except requests.exceptions.RequestException as e:
                logger.warning(f"Request exception: {e}, attempt {attempt + 1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
                else:
                    raise
        
        return response

    def get_folders(self) -> List[Dict]:
        """Získá seznam všech složek"""
        try:
            response = self._make_request('GET', 'folders')
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Načteno {len(data.get('list', []))} složek")
                return data.get('list', [])
            else:
                logger.error(f"Chyba při načítání složek: {response.status_code}")
                logger.error(response.text)
                return []
                
        except Exception as e:
            logger.error(f"Exception při načítání složek: {str(e)}")
            return []

    def get_charts_in_folder(self, folder_id: str) -> List[ChartInfo]:
        """Získá všechny grafy ze složky s kompletními detaily"""
        try:
            response = self._make_request('GET', f'folders/{folder_id}')

            if response.status_code == 200:
                data = response.json()
                charts = []

                # Zpracování grafů ze složky
                chart_ids = [item['id'] for item in data.get('charts', [])]
                logger.info(f"Načítám detaily pro {len(chart_ids)} grafů ze složky {folder_id}")

                for chart_id in chart_ids:
                    try:
                        # Načtení detailů každého grafu
                        chart_response = self._make_request('GET', f'charts/{chart_id}')

                        if chart_response.status_code == 200:
                            chart_data = chart_response.json()

                            chart_info = ChartInfo(
                                id=chart_id,
                                title=chart_data.get('title', 'Bez názvu'),
                                type=chart_data.get('type', 'unknown'),
                                status=chart_data.get('publicStatus', 'draft'),
                                url=chart_data.get('url', ''),
                                share_url=chart_data.get('publicUrl', '')
                            )
                            charts.append(chart_info)
                        else:
                            logger.warning(f"Nepodařilo se načíst detaily grafu {chart_id}: {chart_response.status_code}")
                            # Vytvoříme základní ChartInfo bez share_url
                            chart_info = ChartInfo(
                                id=chart_id,
                                title=f'Graf {chart_id}',
                                type='unknown',
                                status='draft',
                                url='',
                                share_url=''
                            )
                            charts.append(chart_info)

                    except Exception as e:
                        logger.warning(f"Chyba při načítání detailů grafu {chart_id}: {str(e)}")
                        # Vytvoříme základní ChartInfo
                        chart_info = ChartInfo(
                            id=chart_id,
                            title=f'Graf {chart_id}',
                            type='unknown',
                            status='draft',
                            url='',
                            share_url=''
                        )
                        charts.append(chart_info)

                logger.info(f"Načteno {len(charts)} grafů ze složky {folder_id}")
                return charts
            else:
                logger.error(f"Chyba při načítání grafů ze složky {folder_id}: {response.status_code}")
                logger.error(response.text)
                return []

        except Exception as e:
            logger.error(f"Exception při načítání grafů ze složky {folder_id}: {str(e)}")
            return []

    def get_chart_metadata(self, chart_id: str) -> Optional[Dict]:
        """Získá metadata grafu"""
        try:
            response = self._make_request('GET', f'charts/{chart_id}')
            
            if response.status_code == 200:
                data = response.json()
                logger.debug(f"Načtena metadata pro graf {chart_id}")
                return data.get('metadata', {})
            else:
                logger.error(f"Chyba při načítání metadata grafu {chart_id}: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Exception při načítání metadata grafu {chart_id}: {str(e)}")
            return None

    def update_chart_metadata(self, chart_id: str, metadata: Dict) -> bool:
        """Aktualizuje metadata grafu"""
        try:
            payload = {"metadata": metadata}
            response = self._make_request('PATCH', f'charts/{chart_id}', json=payload)
            
            if response.status_code == 200:
                logger.info(f"Metadata grafu {chart_id} aktualizována")
                return True
            else:
                logger.error(f"Chyba při aktualizaci metadata grafu {chart_id}: {response.status_code}")
                logger.error(response.text)
                return False
                
        except Exception as e:
            logger.error(f"Exception při aktualizaci metadata grafu {chart_id}: {str(e)}")
            return False

    def publish_chart(self, chart_id: str) -> Optional[str]:
        """Publikuje graf a vrátí share URL"""
        try:
            response = self._make_request('POST', f'charts/{chart_id}/publish')
            
            if response.status_code == 200:
                data = response.json()
                share_url = data.get('url', data.get('publicUrl'))
                logger.info(f"Graf {chart_id} publikován: {share_url}")
                return share_url
            else:
                logger.error(f"Chyba při publikování grafu {chart_id}: {response.status_code}")
                logger.error(response.text)
                return None
                
        except Exception as e:
            logger.error(f"Exception při publikování grafu {chart_id}: {str(e)}")
            return None

    def unpublish_chart(self, chart_id: str) -> bool:
        """Unpublikuje graf"""
        try:
            response = self._make_request('DELETE', f'charts/{chart_id}/publish')

            if response.status_code in [200, 204]:
                logger.info(f"Graf {chart_id} unpublikován")
                return True
            elif response.status_code == 404:
                # Graf už není publikován nebo neexistuje - to je OK
                logger.info(f"Graf {chart_id} už není publikován (404)")
                return True
            else:
                logger.error(f"Chyba při unpublikování grafu {chart_id}: {response.status_code}")
                logger.error(response.text)
                return False

        except Exception as e:
            logger.error(f"Exception při unpublikování grafu {chart_id}: {str(e)}")
            return False

    def find_survey_folder(self, survey_id: str) -> Optional[str]:
        """Najde složku pro daný survey ID v LimeSurvey parent složce"""
        try:
            # Hledáme v parent složce (LimeSurvey folder)
            parent_folder_id = self.limesurvey_folder_id

            if not parent_folder_id:
                logger.error("DATAWRAPPER_LIMESURVEY_FOLDER_ID není nastaveno")
                return None

            # Načtení parent složky
            response = self._make_request('GET', f'folders/{parent_folder_id}')

            if response.status_code != 200:
                logger.error(f"Nepodařilo se načíst parent složku {parent_folder_id}")
                return None

            parent_data = response.json()
            children_ids = [child.get('id') for child in parent_data.get('children', [])]

            logger.debug(f"Hledám survey {survey_id} v {len(children_ids)} podsložkách")

            # Hledání podle názvu v podsložkách
            for child_id in children_ids:
                child_response = self._make_request('GET', f'folders/{child_id}')

                if child_response.status_code == 200:
                    child_data = child_response.json()
                    folder_name = child_data.get('name', '')

                    # Hledáme přesnou shodu nebo obsahuje survey_id
                    if folder_name == survey_id or survey_id in folder_name:
                        logger.info(f"Nalezena složka pro survey {survey_id}: '{folder_name}' (ID: {child_id})")
                        return str(child_id)

            logger.warning(f"Složka pro survey {survey_id} nebyla nalezena v parent složce {parent_folder_id}")
            return None

        except Exception as e:
            logger.error(f"Exception při hledání složky pro survey {survey_id}: {str(e)}")
            return None
