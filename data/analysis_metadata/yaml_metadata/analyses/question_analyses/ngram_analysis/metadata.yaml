id: NGA
code: ngram_analysis
name: N-gram analýza
description: Analýza častých slovních spojení
detailedDescription: 'N-gram analýza je technika používaná v zpracování přirozeného
  jazyka (NLP) ke studiu sekvencí n po sobě jdoucích prvků (nejčastěji slov nebo znaků)
  v textu. N-gramy mohou být unigramy (jednotlivá slova), bigramy (dvojice slov),
  trigramy (trojice slov) atd.


  Tato analýza je klíčová pro mnoho aplikací NLP, v<PERSON><PERSON>n<PERSON> predikce textu, strojového
  překladu a analýzy stylu. Pomáhá identifikovat běžné fráze, kolokace a vzorce v
  používání jazyka, což je užitečné pro porozumění struktury a významu textu.'
version: 1.0.0
author: system
created: '2024-11-15T14:54:59.494901'
modified: '2024-11-15T14:54:59.494906'
dependencies: []
inputRequirements: []
outputFormat:
  type: object
  schema: {}
supportedVisualizations:
- id: TNG
  code: ngram_table
  mapping: {}
- id: NSG
  code: ngram_network_graph
  mapping: {}
- id: HBA
  code: horizontal_bar_chart
  mapping: {}
- id: TRM
  code: treemap
  mapping: {}
