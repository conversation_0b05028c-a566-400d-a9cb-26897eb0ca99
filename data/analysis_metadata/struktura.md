# Struktura  a vazby mezi ot<PERSON><PERSON>, anal<PERSON><PERSON><PERSON>, vizualizací, NLP a parametry

## Tvorba vyhodnocení na základě struktury průzkumu a csv výsledných dat
* vycházíme ze dvou exportů Limesurvey: a)struktura průzkumu ve formátu .lss a b)csv výsledných dat ve formátu .csv
* vyhodnocení celého průzkumu můžeme rozdělit na provedení analýz a vizualizací otázku po otázce doplněné o NLP zpracování pomocí AI, kter<PERSON> je využito jak v procesu analýzy, tak v procesu sestavení závěrečného dokumentu vyhodnocení. 
* Závěrečný dokument vyhodnocení můžeme rozdělit do sekcí, v každé sekci můžeme zpracovávat otázku po ot<PERSON><PERSON><PERSON>, kter<PERSON> jsou do sekce zařazeny a určit požadavky NLP pro vytvoření obsahu sekce.
* Každá otázka má v Limesurvey definovaný typ otázky, a ke každému typu jsou povoleny možnost provedení konkrétních analýz. Každá analýza má své možné vizualizace - grafy, tabulky, textové výstupy výsledných dat z analýzy. Seznam všech typů otázek, analýz a vizualizací je v tomto dokumentu níže. Dokument také obsahuje vyzby, které otázky mají povolené jaké analýzy a jaké analýzy mohou mít jaké vizualizace.
* Výstupy některých analýz mohou být použity jako vstup pro další analýzy nebo NLP zpracování, pro každou otázku lze zpracovat více analýz i více vizualizací.
* každá analýza a vizualizace má své parametry, které se ukládají v JSON formátu
* existuje pevný seznam analýz a vizualizací, které se postupně kódují a implementují

### Příklad sekce "Účastníci průzkumu"
```json
{
    "id": "SEC1",
    "název": "Účastníci průzkumu",
    "popis": "Demografické údaje a charakteristiky respondentů",
    "otázky": ["Q1", "Q2", "Q4", "Q5", "Q6"],
    "cíl_nlp": "Vytvořte souhrnný popis účastníků průzkumu včetně jejich demografických charakteristik a relevantních statistik.",
    "kontext_nlp": "Průzkum se zaměřuje na uživatele produktu X v České republice.",
    "formát_výstupu": {
        "počet_respondentů": "integer",
        "demografické_údaje": {
            "věkové_skupiny": "object",
            "pohlaví": "object",
            "region": "object"
        },
        "textový_popis": "string"
    }
}
```

### Otázka v sekci
```json
{
    "id": "Q1",
    "sekce": "SEC1",
    "pořadí": 1,
    "analýzy": [
        {
            "typ": "FrequencyAnalysis",
            "parametry": {
                "min_frequency": 1,
                "sort_order": "descending"
            }
        },
        {
            "typ": "ThematicAnalysis",
            "parametry": {
                "min_theme_frequency": 2,
                "max_themes": 10
            }
        }
    ],
    "vizualizace": [
        {
            "typ": "bar",
            "parametry": {
                "figsize": [10, 6],
                "rotation": 45,
                "color": "#1f77b4"
            }
        },
        {
            "typ": "wordcloud",
            "parametry": {
                "width": 800,
                "height": 400,
                "background_color": "white"
            }
        }
    ],
    "nlp": {
        "cíl": "Analyzujte odpovědi na otázku o věku respondentů a identifikujte hlavní věkové skupiny.",
        "kontext": "Věk je důležitý pro segmentaci uživatelů produktu X.",
        "formát_výstupu": {
            "věkové_skupiny": {
                "název": "string",
                "počet": "integer",
                "procento": "float"
            },
            "průměrný_věk": "float",
            "medián_věku": "float",
            "textový_popis": "string"
        }
    }
}
```

## tvorba struktury průzkumu .lss pomocí AI
* Vytváříme průzkum ve formátu.lss a exportujeme ho do Limesurvey.
* Uživatel v rámci chatu s AI (text, zvuk) zadá požadované cíle a další požadavky na vytvoření průzkumu.
* AI analyzuje zadané cíle a požadavky a vygeneruje parametry pro vytvoření struktury průzkumu.lss.
* Tyto parametry jsou editovatelné za pomocí chatu s AI nebo editoru těchto struktrur.
* Pokud jsou parametry v pořádku, vygenerujeme strukturu průzkumu.lss.
* vycházíme ze znalosti všech typů možných otázek v Limesurvey a povolených parametrů pro každý typ otázky a na druhé straně ze znalosti XML struktury.lss.
* Požadavky uživatele zpracujeme pomocí AI a vygenerujeme strukturu průzkumu.lss.


## NLP zpracování
- NLP a využití AI je podrobě rozepsáno v daších částech dokumentace (doplnit odkazy na soubory)



## Typy otázek Limesurvey

| ID | Název | Popis a použití |
|----|-------|----------------|
| 1 | Array Dual Scale | Matice otázek s dvěma škálami hodnocení pro každou položku |
| 5 | 5 Point Choice | Jednoduchá otázka s 5bodovou škálou (Likertova škála) |
| A | Array (5 Point) | Matice otázek s 5bodovou škálou pro každou položku |
| B | Array (10 Point) | Matice otázek s 10bodovou škálou pro každou položku |
| C | Array (Y/N/U) | Matice otázek s možnostmi Ano/Ne/Nevím |
| D | Date | Zadání data ve standardním formátu |
| E | Array (I/S/D) | Matice pro hodnocení změny (Zvýšení/Stejné/Snížení) |
| F | Array (Flex. Labels) | Matice s vlastními popisky škály |
| G | Gender | Výběr pohlaví |
| H | Array (Flex. by Col) | Matice s vlastními popisky pro sloupce |
| I | Language Switch | Přepínač jazyka dotazníku |
| K | Multiple Numerical | Více číselných vstupů |
| L | List (Radio) | Seznam s výběrem jedné možnosti |
| M | Multiple choice | Seznam s možností výběru více odpovědí |
| N | Numerical Input | Jeden číselný vstup |
| O | List With Comment | Seznam s výběrem jedné možnosti a polem pro komentář |
| P | Multiple choice + comments | Seznam s více možnostmi a komentáři |
| Q | Multiple Short Text | Více krátkých textových odpovědí |
| R | Ranking | Seřazení položek podle priority |
| S | Short Free Text | Krátká textová odpověď |
| T | Long Free Text | Dlouhá textová odpověď |
| U | Huge Free Text | Velmi dlouhá textová odpověď |
| X | Boilerplate Question | Informativní text bez možnosti odpovědi |
| Y | Yes/No | Jednoduchá otázka Ano/Ne |
| ! | List (Dropdown) | Rozbalovací seznam s jednou možností výběru |
| : | Array (Flex. DD) | Matice s rozbalovacími seznamy |
| ; | Array (Flex. Text) | Matice s textovými poli |
| \| | File Upload | Nahrání souboru |


## Typy analýz (konečný výčet k implementaci včetně priority implementace)
| ID  | Název | Možné vizualizace (ID) | Popis | Priorita |
|-----|--------|----------------------|--------|----------|
| ANA | ANOVA/MANOVA | BOX, INT, RSP, QQP | Analýza rozptylu | 5 |
| CLA | Klastrová analýza | DEN, SCP, HMP, 3DC | Seskupování podobných odpovědí | 4 |
| CMA | Komparativní analýza | GBA, RAD, PAC, BFC | Porovnání mezi skupinami | 3 |
| COA | Kontextová analýza | KWC, CTR, CNT, DPG | Analýza kontextu výskytu | 4 |
| CRA | Korelační analýza | CRM, SPM, BUB, HMP | Analýza vztahů mezi proměnnými | 3 |
| CTA | Kontingenční analýza | CTT, MOZ, GBA, HMP | Analýza vztahů mezi kategoriemi | 3 |
| DIA | Distribuční analýza | HIS, DEN, BOX, VIP | Analýza rozložení hodnot | 2 |
| FAA | Faktorová analýza | SCP, BIP, LDP, FAM | Identifikace latentních faktorů | 5 |
| FRA | Frekvenční analýza | PAR, BAR, TFT, BUB | Počítání výskytů slov, frází nebo hodnot | 1 |
| GEA | Geografická analýza | GMP, GMH, GMB, GMC | Analýza geografického rozložení | 4 |
| NGA | N-gram analýza | TNG, NSG, HBA, TRM | Analýza častých slovních spojení | 3 |
| NTA | Síťová analýza | NET, CHD, SAN, FDG | Analýza vztahů mezi prvky | 4 |
| QCA | Kvalitativní kódování | CMX, CRB, CLP, ALL | Systematická kategorizace odpovědí | 2 |
| RAA | Ranking analýza | WAT, DIV, DUM, HMP | Analýza pořadí a preferencí | 3 |
| SNA | Sentiment analýza | BAR, TSL, HMP, PIE | Hodnocení emočního zabarvení odpovědí | 2 |
| STA | Summary statistics | BOX, VIP, HIS, DEN | Základní statistické ukazatele | 1 |
| THA | Tematická analýza | HTR, MMD, TFT, SUN | Identifikace a kategorizace opakujících se témat | 1 |
| TMA | Časová analýza | TML, STG, BPC, ANV | Analýza vývoje v čase | 2 |
| TPA | Topic modeling | LDA, TBC, TTS, PYR | Strojové učení pro identifikaci témat | 4 |
| WCA | Word Cloud analýza | WCS, WCT, WCH, WCT | Analýza četnosti slov a jejich vizuální reprezentace | 2 |

## Typy vizualizací (konečný výčet k implementaci včetně priority implementace)
| ID  | Název | Popis | Priorita |
|-----|--------|--------|----------|
| 3DC | 3D Clustering | 3D vizualizace shluků | 4 |
| ALL | Alluvial diagram | Diagram toků mezi kategoriemi | 4 |
| ANV | Animovaná vizualizace | Časově animovaná vizualizace | 5 |
| BAR | Sloupcový graf | Vertikální sloupce pro porovnání hodnot | 1 |
| BFC | Butterfly chart | Graf pro porovnání dvou distribucí | 3 |
| BIP | Biplot | Graf pro faktorovou analýzu | 5 |
| BOX | Box plot | Krabicový graf | 1 |
| BPC | Bump chart | Graf pro zobrazení změn pořadí v čase | 4 |
| BUB | Bubble chart | Bodový graf s variabilní velikostí bodů | 3 |
| CHD | Chord diagram | Kruhový diagram vztahů | 4 |
| CLP | Code line plot | Časový graf použití kódů | 3 |
| CMX | Code matrix | Matice kódů | 3 |
| CNT | Kolokační síť | Síť souvisejících výrazů | 4 |
| CRB | Code relations | Graf vztahů mezi kódy | 3 |
| CRM | Korelační matice | Matice korelačních koeficientů | 3 |
| CTR | Kontextový strom | Strom kontextových vazeb | 4 |
| CTT | Kontingenční tabulka | Tabulka četností kombinací kategorií | 2 |
| DEN | Dendrogram | Stromový diagram pro hierarchické shluky | 3 |
| DIV | Diverging bars | Graf odchylek od středu | 2 |
| DPG | Dependency graph | Graf závislostí | 4 |
| DUM | Dumbbell plot | Graf pro porovnání dvou hodnot | 2 |
| FAM | Factor map | Mapa faktorů | 5 |
| FDG | Force-directed graph | Síťový graf s fyzikální simulací | 4 |
| GBA | Grouped bar chart | Seskupený sloupcový graf | 2 |
| GMB | Geo mapa bublinová | Mapa s bublinami různé velikosti | 3 |
| GMC | Geo mapa choropleth | Mapa s barevně odlišenými regiony | 3 |
| GMH | Geo mapa heat | Teplotní mapa na geografickém podkladu | 3 |
| GMP | Geo mapa bodová | Mapa s bodovými značkami | 2 |
| HBA | Horizontální bar | Horizontální sloupce pro porovnání hodnot | 1 |
| HIS | Histogram | Graf četností intervalů | 1 |
| HMP | Heat mapa | Barevná mapa intenzity hodnot | 2 |
| HTR | Hierarchický strom | Stromová struktura témat a podtémat | 2 |
| INT | Interaction plot | Graf interakcí | 4 |
| KWC | KWIC display | Zobrazení klíčového slova v kontextu | 3 |
| LDA | LDA vizualizace | Vizualizace témat z LDA analýzy | 5 |
| LDP | Loading plot | Graf faktorových zátěží | 5 |
| MMD | Myšlenková mapa | Vizualizace propojení mezi tématy | 2 |
| MOZ | Mozaikový plot | Vizualizace kontingenční tabulky | 3 |
| NET | Network graph | Síťový graf vztahů | 4 |
| NSG | N-gram síťový graf | Vizualizace vztahů mezi n-gramy | 4 |
| PAC | Parallel coordinates | Graf rovnoběžných souřadnic | 4 |
| PAR | Pareto diagram | Graf pro analýzu významnosti faktorů | 2 |
| PIE | Koláčový graf | Zobrazení podílů na celku | 1 |
| PYR | Pyramidový graf | Hierarchický graf témat | 3 |
| QQP | Q-Q plot | Graf pro ověření normality | 3 |
| RAD | Radar chart | Paprskový graf pro více dimenzí | 3 |
| RSP | Residual plot | Graf reziduí | 4 |
| SAN | Sankey diagram | Diagram toků mezi kategoriemi | 3 |
| SCP | Scatter plot | Bodový graf pro zobrazení vztahů | 2 |
| SPM | Scatter plot matrix | Matice bodových grafů | 4 |
| STG | Stream graph | Graf pro zobrazení více časových řad | 4 |
| SUN | Sunburst diagram | Hierarchické zobrazení v kruhové podobě | 3 |
| TAB | Tabulka | Základní tabulkový výstup | 1 |
| TBC | Topic bubble chart | Bublinkový graf témat | 4 |
| TFT | Tabulka frekvencí | Přehledná tabulka četností | 1 |
| TML | Timeline | Časová osa událostí | 2 |
| TNG | Tabulka n-gramů | Přehled častých slovních spojení | 2 |
| TRM | Treemap | Hierarchická vizualizace pomocí obdélníků | 3 |
| TSL | Časová osa | Lineární zobrazení vývoje v čase | 2 |
| TTS | Topic timeline | Časová osa vývoje témat | 4 |
| TXT | Text | Textový výstup | 1 |
| VIP | Violin plot | Houslovitý graf distribuce | 3 |
| WAT | Waterfall chart | Kaskádový graf | 3 |
| WCH | Word Cloud hierarchický | Hierarchický word cloud | 2 |
| WCS | Word Cloud standard | Klasický word cloud | 1 |
| WCT | Word Cloud tvarovaný | Word cloud v definovaném tvaru | 2 |



## Vazba mezi ID typu otázky, ID analýz a ID vizualizací


| Question ID | Analysis ID | Visualization IDs | Default Viz |
|------------|-------------|-------------------|-------------|
| 1 | CRA | TXT,HMP,RAD,PAC,CRM,SCP,BUB,SPM,BFC,DIV | HMP |
| 1 | CTA | TXT,HMP,CTT,MOZ,GBA,DIV,SAN,ALL,CHD | CTT |
| 1 | CMA | TXT,GBA,RAD,PAC,BFC,DIV,DUM,HMP,CRM | RAD |
| 5 | FRA | TXT,BAR,HBA,TFT,PIE,PAR,TRM,DIV,DUM | BAR |
| 5 | STA | TXT,BOX,VIP,HIS,DEN,QQP,RSP,SCP | BOX |
| 5 | DIA | TXT,HIS,BOX,VIP,DEN,QQP,SCP,CRM | HIS |
| A | CRA | TXT,HMP,RAD,CRM,SCP,BUB,SPM,BFC,DIV | HMP |
| A | CTA | TXT,HMP,CTT,MOZ,GBA,DIV,SAN,ALL,CHD | CTT |
| A | FAA | TXT,SCP,BIP,LDP,FAM,PYR,TRM,HMP | SCP |
| B | CRA | TXT,HMP,RAD,CRM,SCP,BUB,SPM,BFC,DIV | HMP |
| B | CTA | TXT,HMP,CTT,MOZ,GBA,DIV,SAN,ALL,CHD | CTT |
| B | FAA | TXT,SCP,BIP,LDP,FAM,PYR,TRM,HMP | SCP |
| C | CTA | TXT,CTT,MOZ,GBA,HMP,DIV,SAN,ALL,CHD | CTT |
| C | FRA | TXT,BAR,HBA,TFT,PIE,PAR,TRM,DIV,DUM | BAR |
| C | THA | TXT,HTR,MMD,TFT,WCS,SUN,TRM,PYR | HTR |
| D | TMA | TXT,TML,TSL,STG,ANV,BPC,TTS,CLP | TSL |
| D | STA | TXT,BOX,VIP,HIS,DEN,QQP,RSP,SCP | BOX |
| E | CTA | TXT,CTT,MOZ,HMP,GBA,DIV,SAN,ALL,CHD | CTT |
| E | RAA | TXT,WAT,DIV,DUM,HMP,BAR,HBA,PAR | DIV |
| E | TMA | TXT,TSL,STG,BPC,TML,ANV,TTS,CLP | TSL |
| F | CRA | TXT,HMP,RAD,PAC,CRM,SCP,BUB,SPM,BFC | HMP |
| F | CTA | TXT,HMP,CTT,MOZ,GBA,DIV,SAN,ALL,CHD | CTT |
| F | CMA | TXT,GBA,RAD,PAC,BFC,DIV,DUM,HMP,CRM | RAD |
| G | FRA | TXT,PIE,BAR,TFT,PAR,TRM,DIV,DUM,HMP | PIE |
| G | CTA | TXT,CTT,MOZ,GBA,DIV,SAN,ALL,CHD,HMP | CTT |
| H | CRA | TXT,HMP,RAD,PAC,CRM,SCP,BUB,SPM,BFC | HMP |
| H | CTA | TXT,HMP,CTT,MOZ,GBA,DIV,SAN,ALL,CHD | CTT |
| H | CMA | TXT,GBA,RAD,PAC,BFC,DIV,DUM,HMP,CRM | RAD |
| K | STA | TXT,BOX,HIS,VIP,DEN,QQP,RSP,SCP,CRM | BOX |
| K | CRA | TXT,SCP,CRM,BUB,HMP,RAD,SPM,BFC,DIV | SCP |
| K | DIA | TXT,HIS,BOX,VIP,DEN,QQP,SCP,CRM,PAC | HIS |
| L | FRA | TXT,PIE,BAR,TFT,PAR,TRM,DIV,DUM,HMP | PIE |
| L | CTA | TXT,CTT,MOZ,GBA,DIV,SAN,ALL,CHD,HMP | CTT |
| L | THA | TXT,HTR,MMD,TFT,WCS,SUN,TRM,PYR,NET | HTR |
| M | FRA | TXT,BAR,TFT,TRM,PIE,PAR,DIV,DUM,HMP | BAR |
| M | THA | TXT,HTR,MMD,TFT,WCS,SUN,TRM,PYR,NET | HTR |
| M | NTA | TXT,NET,CHD,SAN,FDG,CNT,DPG,ALL,MMD | NET |
| N | STA | TXT,HIS,BOX,VIP,DEN,QQP,RSP,SCP,CRM | BOX |
| N | DIA | TXT,HIS,BOX,VIP,DEN,QQP,SCP,CRM,PAC | HIS |
| N | CRA | TXT,SCP,BUB,CRM,HMP,RAD,SPM,BFC,DIV | SCP |
| O | FRA | TXT,BAR,TFT,PIE,PAR,TRM,DIV,DUM,HMP | BAR |
| O | THA | TXT,HTR,MMD,WCS,SUN,TRM,PYR,NET,CNT | HTR |
| O | COA | TXT,KWC,CTR,CNT,DPG,NET,CHD,MMD,HTR | KWC |
| P | FRA | TXT,BAR,TFT,TRM,PIE,PAR,DIV,DUM,HMP | BAR |
| P | THA | TXT,HTR,MMD,WCS,SUN,TRM,PYR,NET,CNT | HTR |
| P | COA | TXT,KWC,CTR,CNT,DPG,NET,CHD,MMD,HTR | KWC |
| Q | THA | TXT,HTR,MMD,WCS,SUN,TRM,PYR,NET,CNT | HTR |
| Q | COA | TXT,KWC,CTR,CNT,DPG,NET,CHD,MMD,HTR | KWC |
| Q | WCA | TXT,WCS,WCT,WCH,HTR,MMD,CNT,TRM,NET | WCS |
| R | RAA | TXT,WAT,DIV,DUM,HMP,BAR,HBA,PAR,TRM | DIV |
| R | CMA | TXT,GBA,RAD,PAC,BFC,DIV,DUM,HMP,CRM | RAD |
| S | THA | TXT,HTR,MMD,WCS,SUN,TRM,PYR,NET,CNT | HTR |
| S | COA | TXT,KWC,CTR,CNT,DPG,NET,CHD,MMD,HTR | KWC |
| S | WCA | TXT,WCS,WCT,WCH,HTR,MMD,CNT,TRM,NET | WCS |
| T | THA | TXT,HTR,MMD,WCS,SUN,TRM,PYR,NET,CNT | HTR |
| T | COA | TXT,KWC,CTR,CNT,DPG,NET,CHD,MMD,HTR | KWC |
| T | TPA | TXT,LDA,TBC,TTS,PYR,HTR,SUN,TRM,NET | LDA |
| U | THA | TXT,HTR,MMD,WCS,SUN,TRM,PYR,NET,CNT | HTR |
| U | TPA | TXT,LDA,TBC,TTS,PYR,HTR,SUN,TRM,NET | LDA |
| U | COA | TXT,KWC,CTR,CNT,DPG,NET,CHD,MMD,HTR | KWC |
| X | - | TXT | TXT |
| Y | FRA | TXT,PIE,BAR,DIV,PAR,TRM,DUM,HMP,GBA | PIE |
| Y | CTA | TXT,CTT,MOZ,GBA,DIV,SAN,ALL,CHD,HMP | CTT |
| ! | FRA | TXT,PIE,BAR,TRM,PAR,DIV,DUM,HMP,GBA | PIE |
| ! | CTA | TXT,CTT,MOZ,GBA,DIV,SAN,ALL,CHD,HMP | CTT |
| ! | THA | TXT,HTR,MMD,TFT,WCS,SUN,TRM,PYR,NET | HTR |
| : | CRA | TXT,HMP,RAD,PAC,CRM,SCP,BUB,SPM,BFC | HMP |
| : | CTA | TXT,HMP,CTT,MOZ,GBA,DIV,SAN,ALL,CHD | CTT |
| : | CMA | TXT,GBA,RAD,PAC,BFC,DIV,DUM,HMP,CRM | RAD |
| ; | THA | TXT,HTR,MMD,WCS,SUN,TRM,PYR,NET,CNT | HTR |
| ; | COA | TXT,KWC,CTR,CNT,DPG,NET,CHD,MMD,HTR | KWC |
| ; | WCA | TXT,WCS,WCT,WCH,HTR,MMD,CNT,TRM,NET | WCS |
| \| | - | TXT | TXT |

Tento dokument je informační, je nutné ho měnit kdykoliv dojde ke změně struktury dat.