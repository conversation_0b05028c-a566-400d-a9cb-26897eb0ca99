# Program Development Prompt: Metadata Structure Generator

## Required Directory Structure

The program must generate the following directory structure dynamically based on all analyses, visualizations and question types found in the input markdown tables. Below is an example structure showing a few entries, but the actual structure should include directories and files for ALL items from the input data:

```
/metadata
├── base-types/
│   ├── question-types.yaml
│   ├── analysis-types.yaml
│   └── visualization-types.yaml
├── parameters/
│   ├── _groups.yaml
│   ├── _inheritance.yaml
│   ├── typography.yaml
│   ├── colors.yaml
│   └── layouts.yaml
├── analyses/
│   ├── _registry.yaml
│   ├── question_analyses/
│   │   ├── frequency-analysis/
│   │   │   ├── metadata.yaml
│   │   │   ├── parameters.yaml
│   │   │   └── visualizations.yaml
│   │   └── descriptive-stats/
│   └── section_analyses/
│       ├── cross-tabulation/
│       │   ├── metadata.yaml
│       │   ├── parameters.yaml
│       │   └── visualizations.yaml
│       └── correlation-analysis/
├── visualizations/
│   ├── _registry.yaml
│   ├── barchart/
│   │   ├── metadata.yaml
│   │   ├── parameters.yaml
│   │   └── supported-data.yaml
│   └── piechart/
└── implementation-status.yaml
```

## Required YAML File Templates

### base-types/question-types.yaml
```yaml
questionTypes:
  - id: string # Mapped from otazky.md ID
    name: string # Mapped from otazky.md Název
    description: string # Mapped from otazky.md Popis a použití
    properties:
      - name: string
        type: string
    supportedAnalyses: string[] # Mapped from otazky2analyzy.md Analysis ID
    status: string # Default "active"
```

### base-types/analysis-types.yaml
```yaml
analysisTypes:
  - id: string # Mapped from analyzy-sekce.md/analyzy-otazky.md ID
    name: string # Mapped from Název
    description: string # Mapped from Popis
    type: "question" | "section" # Based on source file
    supportedQuestionTypes: string[] # Mapped from otazky2analyzy.md
    supportedVisualizations: string[] # Mapped from Možné vizualizace
    parameters: string[]
    status: string # Based on Priorita
```

### base-types/visualization-types.yaml
```yaml
visualizationTypes:
  - id: string # Mapped from vizualizace.md ID
    name: string # Mapped from Název
    description: string # Mapped from Popis
    supportedDataStructures: string[]
    parameters: string[]
    status: string # Based on Priorita
```

### analyses/_registry.yaml
```yaml
analyses:
  question: # From analyzy-otazky.md
    - id: string
      path: string # Constructed path
      status: string # Based on Priorita
  section: # From analyzy-sekce.md
    - id: string
      path: string # Constructed path
      status: string # Based on Priorita
```

### visualizations/_registry.yaml
```yaml
visualizations:
  - id: string # From vizualizace.md ID
    path: string # Constructed path
    status: string # Based on Priorita
```

### analyses/[type]/metadata.yaml
```yaml
id: string # From analyzy-otazky.md/analyzy-sekce.md ID
name: string # From Název
description: string # From Popis
version: "1.0.0"
author: "system"
created: date # Current date
modified: date # Current date
dependencies: string[]
inputRequirements:
  - type: string
    properties: string[]
outputFormat:
  type: string
  schema: object
supportedVisualizations: # From Možné vizualizace
  - id: string
    mapping: object
```

### visualizations/[type]/metadata.yaml
```yaml
id: string # From vizualizace.md ID
name: string # From Název
version: "1.0.0"
description: string # From Popis
author: "system"
created: date # Current date
modified: date # Current date
supportedDataStructures:
  - type: string
    mapping:
      required: object
      optional: object
inputValidation: object
parameters:
  inherits: string[]
  specific: object[]
```

### implementation-status.yaml
```yaml
status:
  questionTypes: # Status for each question type
    [type_id]: string # Based on priorities
  analyses: # Status for each analysis
    [analysis_id]: string # Based on priorities
  visualizations: # Status for each visualization
    [viz_id]: string # Based on priorities
```

## Context
I need to develop a program that will generate a standardized metadata directory structure and corresponding YAML files for a survey analysis system. The program should take input from markdown tables containing information about questions, analyses, and visualizations, and generate a complete metadata structure according to specified templates.

## Input Data Sources
The program will process the following markdown table files:
1. `otazky.md`: Contains question definitions
   - Columns: ID, Název (Name), Popis a použití (Description and Usage)
2. `analyzy-sekce.md`: Section-level analyses
   - Columns: ID, Název (Name), Možné vizualizace (Possible Visualizations), Popis (Description), Priorita (Priority)
3. `analyzy-otazky.md`: Question-level analyses
   - Columns: ID, Název (Name), Možné vizualizace (Possible Visualizations), Popis (Description), Priorita (Priority)
4. `vizualizace.md`: Available visualizations
   - Columns: ID, Název (Name), Popis (Description), Priorita (Priority)
5. `otazky2analyzy.md`: Mapping between questions, analyses and visualizations
   - Columns: Question ID, Analysis ID, Visualization IDs, Default Viz

## Required Functionality

### 1. File Processing
- Implement functions to read and parse markdown tables
- Convert markdown tables into structured data objects
- Validate input data for required fields and format consistency

### 2. Directory Structure Generation
- Create the complete directory structure as specified in the template
- Handle nested directory creation
- Ensure proper permissions and error handling

### 3. YAML File Generation
- Generate YAML files based on templates and input data
- Implement proper YAML formatting and syntax
- Handle data mapping between input sources and output templates

### 4. Data Mapping Requirements

#### Question Types (question-types.yaml)
- Map from `otazky.md`
- Generate unique IDs for question types
- Determine supported analyses from `otazky2analyzy.md`

#### Analysis Types (analysis-types.yaml)
- Combine data from `analyzy-sekce.md` and `analyzy-otazky.md`
- Map supported question types from `otazky2analyzy.md`
- Map supported visualizations from the analysis definitions

#### Visualization Types (visualization-types.yaml)
- Map from `vizualizace.md`
- Generate supported data structures based on usage patterns
- Define parameter requirements

### 5. Required Features

#### Data Validation
- Validate all input data against schema requirements
- Ensure referential integrity between different components
- Validate visualization compatibility with analyses

#### Error Handling
- Provide clear error messages for missing or invalid data
- Handle file system errors gracefully
- Implement logging for debugging and auditing

#### Configuration
- Allow configuration of output paths
- Support customization of template structures
- Enable specification of default values

## Technical Requirements

### Programming Language & Libraries
- Use Python 3.8+ for implementation
- Required libraries:
  - PyYAML for YAML processing
  - pandas for data manipulation
  - pathlib for file system operations
  - logging for error tracking

### Code Structure
```python
class MetadataGenerator:
    def __init__(self, config):
        # Initialize with configuration
        pass

    def read_input_files(self):
        # Read and parse markdown tables
        pass

    def generate_directory_structure(self):
        # Create directory hierarchy
        pass

    def generate_yaml_files(self):
        # Generate YAML files from templates
        pass

    def validate_data(self):
        # Validate input data and relationships
        pass
```

### Error Handling Structure
```python
class MetadataGenerationError(Exception):
    pass

class ValidationError(MetadataGenerationError):
    pass

class FileSystemError(MetadataGenerationError):
    pass
```

## Implementation Steps

1. **Data Loading Phase**
   - Implement markdown table parser
   - Create data structures for holding parsed information
   - Validate input data completeness

2. **Directory Structure Phase**
   - Create base directory structure
   - Set up logging and error handling
   - Implement directory validation

3. **YAML Generation Phase**
   - Implement template processors
   - Create YAML file generators
   - Add validation for generated files

4. **Testing Phase**
   - Unit tests for each component
   - Integration tests for full workflow
   - Validation tests for output structure

## Example Usage

```python
# Configuration
config = {
    'input_dir': './input',
    'output_dir': './metadata',
    'templates_dir': './templates'
}

# Generator instantiation
generator = MetadataGenerator(config)

# Generate structure
try:
    generator.generate()
except MetadataGenerationError as e:
    logging.error(f"Generation failed: {e}")
```

## Output Validation

The program should validate:
1. All required files are generated
2. YAML syntax is correct
3. All references between files are valid
4. Directory structure matches template
5. All required fields are populated

## Error Messages

Provide clear error messages for common issues:
- Missing input files
- Invalid markdown table format
- Missing required fields
- Invalid relationships between components
- File system permission issues

## Documentation Requirements

The program should generate:
1. Log file of all operations
2. Validation report
3. Summary of generated structure
4. List of any warnings or potential issues

## Additional Considerations

### Performance
- Implement efficient data structures for lookup operations
- Use generators for large file processing
- Implement caching where appropriate

### Maintainability
- Use clear code structure and documentation
- Implement modular design for easy updates
- Use type hints and docstrings

### Extensibility
- Allow for new template types
- Support custom validation rules
- Enable plugin architecture for specialized processors